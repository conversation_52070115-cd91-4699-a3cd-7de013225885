<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>تواصل معنا - مؤسسة ركن النحت</title>
    <meta
      name="description"
      content="تواصل مع مؤسسة ركن النحت للاستفسارات والطلبات"
    />
    <meta
      name="keywords"
      content="تواصل, اتصال, استفسارات, طلبات, خدمة العملاء, فهد منصور, ركن النحت"
    />
    <meta name="author" content="فهد منصور" />

    <!-- Canonical Link -->
    <link rel="canonical" href="https://fahdmansor.surge.sh/contact.html" />

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="تواصل معنا - مؤسسة ركن النحت" />
    <meta
      property="og:description"
      content="تواصل مع مؤسسة ركن النحت للاستفسارات والطلبات"
    />
    <meta property="og:image" content="assets/site/profile2.png" />
    <meta property="og:type" content="website" />
    <meta
      property="og:url"
      content="https://fahdmansor.surge.sh/contact.html"
    />

    <!-- SEO Meta Tags -->
    <meta name="robots" content="index, follow" />

    <!-- أيقونات الموقع -->
    <link rel="icon" type="image/png" href="assets/site/favicon.png" />
    <link rel="manifest" href="manifest.json" />
    <meta name="theme-color" content="#FBBF24" />

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- تحميل الخطوط محلياً -->
    <link
      rel="preload"
      href="assets/fonts/Cairo-Regular.woff2"
      as="font"
      type="font/woff2"
      crossorigin
    />
    <link
      rel="preload"
      href="assets/fonts/Cairo-Bold.woff2"
      as="font"
      type="font/woff2"
      crossorigin
    />

    <!-- نظام إدارة اللغات -->
    <script src="assets/js/translations.js"></script>
    <script src="assets/js/language-manager.js"></script>
    <link rel="stylesheet" href="assets/css/language-styles.css" />

    <style>
      @font-face {
        font-family: "Cairo";
        src: url("assets/fonts/Cairo-Regular.woff2") format("woff2");
        font-weight: 400;
        font-style: normal;
        font-display: swap;
      }
      @font-face {
        font-family: "Cairo";
        src: url("assets/fonts/Cairo-Bold.woff2") format("woff2");
        font-weight: 700;
        font-style: normal;
        font-display: swap;
      }
      body {
        font-family: "Cairo", "sans-serif";
      }
    </style>

    <script>
      tailwind.config = {
        theme: {
          extend: {
            fontFamily: {
              sans: ["Cairo", "sans-serif"],
            },
            colors: {
              "brand-dark": "#111827",
              "brand-light": "#ffffff",
              "brand-accent": {
                DEFAULT: "#F59E0B",
                start: "#FBBF24",
                end: "#D97706",
              },
            },
          },
        },
      };
    </script>
  </head>

  <body class="bg-brand-dark text-brand-light">
    <!-- شريط التنقل -->
    <nav
      class="fixed top-0 left-0 right-0 z-50 bg-brand-dark/90 backdrop-blur-md border-b border-brand-accent/20"
    >
      <div class="container mx-auto px-4 py-3">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4 space-x-reverse">
            <img
              src="assets/site/profile2.png"
              alt="مؤسسة ركن النحت"
              class="w-12 h-12 rounded-full border-2 border-brand-accent"
            />
            <h1
              class="text-xl font-bold text-brand-accent"
              data-translate="siteTitle"
            >
              مؤسسة ركن النحت
            </h1>
          </div>
          <div class="hidden md:flex items-center space-x-6 space-x-reverse">
            <a
              href="index.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              data-translate="navHome"
              >الرئيسية</a
            >
            <a
              href="sculptures.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              data-translate="navSculptures"
              >قسم النحت</a
            >
            <a
              href="decorations.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              data-translate="navDecorations"
              >قسم الديكور</a
            >
            <a
              href="gallery.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              data-translate="navGallery"
              >معرض الأعمال</a
            >
            <a
              href="contact.html"
              class="text-brand-accent font-bold"
              data-translate="navContact"
              >تواصل معنا</a
            >
          </div>
          <!-- زر تبديل اللغة -->
          <button
            id="lang-switch-btn"
            class="ml-4 px-4 py-2 rounded-lg bg-brand-accent text-brand-dark font-bold hover:bg-brand-accent/90 transition-all"
          >
            English
          </button>
          <button id="mobile-menu-btn" class="md:hidden text-brand-light">
            <svg
              class="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 6h16M4 12h16M4 18h16"
              ></path>
            </svg>
          </button>
        </div>

        <!-- القائمة المحمولة -->
        <div id="mobile-menu" class="hidden md:hidden mt-4 pb-4">
          <div class="flex flex-col space-y-3">
            <a
              href="index.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              data-translate="navHome"
              >الرئيسية</a
            >
            <a
              href="sculptures.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              data-translate="navSculptures"
              >قسم النحت</a
            >
            <a
              href="decorations.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              data-translate="navDecorations"
              >قسم الديكور</a
            >
            <a
              href="gallery.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              data-translate="navGallery"
              >معرض الأعمال</a
            >
            <a
              href="contact.html"
              class="text-brand-accent font-bold"
              data-translate="navContact"
              >تواصل معنا</a
            >
          </div>
        </div>
      </div>
    </nav>

    <!-- القسم الرئيسي -->
    <section class="pt-24 pb-12 bg-gradient-to-b from-brand-dark to-gray-900">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h1
            class="text-5xl font-bold text-brand-accent mb-4"
            data-translate="contactTitle"
          >
            📞 تواصل معنا
          </h1>
          <p
            class="text-xl text-brand-light/80 max-w-3xl mx-auto"
            data-translate="contactDescription"
          >
            نحن هنا للإجابة على استفساراتكم ومساعدتكم في تحقيق رؤيتكم الفنية
          </p>
        </div>
      </div>
    </section>

    <!-- قسم معلومات التواصل -->
    <section class="py-16 bg-brand-dark">
      <div class="container mx-auto px-4">
        <div class="grid md:grid-cols-2 gap-12">
          <!-- معلومات التواصل -->
          <div>
            <h2
              class="text-3xl font-bold text-brand-accent mb-8"
              data-translate="contactInfoTitle"
            >
              معلومات التواصل
            </h2>
            <div class="space-y-6">
              <div class="flex items-center space-x-4 space-x-reverse">
                <div
                  class="w-12 h-12 bg-brand-accent rounded-full flex items-center justify-center"
                >
                  <svg
                    class="w-6 h-6 text-brand-dark"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"
                    ></path>
                  </svg>
                </div>
                <div>
                  <h3
                    class="text-lg font-semibold text-brand-light"
                    data-translate="phone"
                  >
                    الهاتف
                  </h3>
                  <p class="text-brand-light/80" dir="ltr">+967 776 245 622</p>
                </div>
              </div>

              <div class="flex items-center space-x-4 space-x-reverse">
                <div
                  class="w-12 h-12 bg-brand-accent rounded-full flex items-center justify-center"
                >
                  <svg
                    class="w-6 h-6 text-brand-dark"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"
                    ></path>
                    <path
                      d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"
                    ></path>
                  </svg>
                </div>
                <div>
                  <h3
                    class="text-lg font-semibold text-brand-light"
                    data-translate="email"
                  >
                    البريد الإلكتروني
                  </h3>
                  <p class="text-brand-light/80" dir="ltr">
                    <EMAIL>
                  </p>
                </div>
              </div>

              <div class="flex items-center space-x-4 space-x-reverse">
                <div
                  class="w-12 h-12 bg-brand-accent rounded-full flex items-center justify-center"
                >
                  <svg
                    class="w-6 h-6 text-brand-dark"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                </div>
                <div>
                  <h3
                    class="text-lg font-semibold text-brand-light"
                    data-translate="address"
                  >
                    العنوان
                  </h3>
                  <p
                    class="text-brand-light/80"
                    data-translate="addressDetails"
                  >
                    اليمن
                  </p>
                </div>
              </div>
            </div>
          </div>

          <!-- نموذج التواصل -->
          <div>
            <h2
              class="text-3xl font-bold text-brand-accent mb-8"
              data-translate="contactFormTitle"
            >
              أرسل لنا رسالة
            </h2>
            <form class="space-y-6" id="contact-form">
              <div>
                <label
                  for="name"
                  class="block text-sm font-medium text-brand-light mb-2"
                  data-translate="contactName"
                  >الاسم</label
                >
                <input
                  type="text"
                  id="name"
                  name="name"
                  required
                  class="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-accent focus:border-transparent text-brand-light placeholder-gray-400"
                  data-translate="contactName"
                  placeholder="الاسم"
                />
              </div>

              <div>
                <label
                  for="email"
                  class="block text-sm font-medium text-brand-light mb-2"
                  data-translate="contactEmail"
                  >البريد الإلكتروني</label
                >
                <input
                  type="email"
                  id="email"
                  name="email"
                  required
                  class="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-accent focus:border-transparent text-brand-light placeholder-gray-400"
                  data-translate="contactEmail"
                  placeholder="البريد الإلكتروني"
                />
              </div>

              <div>
                <label
                  for="phone"
                  class="block text-sm font-medium text-brand-light mb-2"
                  data-translate="contactPhone"
                  >رقم الهاتف</label
                >
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  class="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-accent focus:border-transparent text-brand-light placeholder-gray-400"
                  data-translate="contactPhone"
                  placeholder="رقم الهاتف"
                />
              </div>

              <div>
                <label
                  for="message"
                  class="block text-sm font-medium text-brand-light mb-2"
                  data-translate="contactMessage"
                  >الرسالة</label
                >
                <textarea
                  id="message"
                  name="message"
                  rows="5"
                  required
                  class="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-accent focus:border-transparent text-brand-light placeholder-gray-400 resize-none"
                  data-translate="contactMessage"
                  placeholder="الرسالة"
                ></textarea>
              </div>

              <button
                type="submit"
                class="w-full bg-brand-accent hover:bg-brand-accent/90 text-brand-dark px-8 py-4 rounded-lg font-bold text-lg transition-all duration-300 transform hover:scale-105"
                data-translate="btnSendMessage"
              >
                📧 إرسال الرسالة
              </button>
            </form>
          </div>
        </div>
      </div>
    </section>

    <!-- الفوتر -->
    <footer class="bg-gray-900 py-12">
      <div class="container mx-auto px-4">
        <div class="grid md:grid-cols-3 gap-8">
          <div>
            <h3
              class="text-xl font-bold text-brand-accent mb-4"
              data-translate="aboutUsTitle"
            >
              عن مؤسسة ركن النحت
            </h3>
            <p class="text-brand-light/80" data-translate="footerDescription">
              متخصصون في فن النحت الحديث والديكور الإبداعي، نقدم خدمات فنية
              متميزة تجمع بين الأصالة والابتكار.
            </p>
          </div>

          <div>
            <h3
              class="text-xl font-bold text-brand-accent mb-4"
              data-translate="quickLinksTitle"
            >
              روابط سريعة
            </h3>
            <ul class="space-y-2">
              <li>
                <a
                  href="index.html"
                  class="text-brand-light/80 hover:text-brand-accent transition-colors"
                  data-translate="navHome"
                  >الرئيسية</a
                >
              </li>
              <li>
                <a
                  href="sculptures.html"
                  class="text-brand-light/80 hover:text-brand-accent transition-colors"
                  data-translate="navSculptures"
                  >قسم النحت</a
                >
              </li>
              <li>
                <a
                  href="decorations.html"
                  class="text-brand-light/80 hover:text-brand-accent transition-colors"
                  data-translate="navDecorations"
                  >قسم الديكور</a
                >
              </li>
              <li>
                <a
                  href="gallery.html"
                  class="text-brand-light/80 hover:text-brand-accent transition-colors"
                  data-translate="navGallery"
                  >معرض الأعمال</a
                >
              </li>
            </ul>
          </div>

          <div>
            <h3
              class="text-xl font-bold text-brand-accent mb-4"
              data-translate="contactInfoTitle"
            >
              معلومات التواصل
            </h3>
            <div class="space-y-2 text-brand-light/80">
              <p data-translate="phoneLabel">📞 الهاتف: 776245622</p>
              <p data-translate="emailLabel">📧 البريد: <EMAIL></p>
              <p data-translate="locationLabel">📍 الموقع: اليمن</p>
            </div>
          </div>
        </div>

        <div class="border-t border-brand-accent/20 mt-8 pt-8 text-center">
          <p class="text-brand-light/60" data-translate="footerCopyright">
            جميع الحقوق محفوظة © 2024 مؤسسة ركن النحت - فهد منصور
          </p>
        </div>
      </div>
    </footer>

    <!-- JavaScript للتفاعل -->
    <script>
      // تفعيل القائمة المحمولة
      document
        .getElementById("mobile-menu-btn")
        .addEventListener("click", function () {
          const mobileMenu = document.getElementById("mobile-menu");
          mobileMenu.classList.toggle("hidden");
        });

      // معالجة نموذج التواصل
      document
        .getElementById("contact-form")
        .addEventListener("submit", function (e) {
          e.preventDefault();

          // جمع بيانات النموذج
          const formData = new FormData(this);
          const name = formData.get("name");
          const email = formData.get("email");
          const phone = formData.get("phone");
          const message = formData.get("message");

          // إنشاء رابط mailto
          const subject = encodeURIComponent("استفسار من موقع مؤسسة ركن النحت");
          const body = encodeURIComponent(`
الاسم: ${name}
البريد الإلكتروني: ${email}
رقم الهاتف: ${phone || "غير محدد"}

الرسالة:
${message}
        `);

          const mailtoLink = `mailto:<EMAIL>?subject=${subject}&body=${body}`;
          window.location.href = mailtoLink;

          // إظهار رسالة نجاح
          alert("تم فتح تطبيق البريد الإلكتروني. شكراً لتواصلكم معنا!");
        });
    </script>
    <script>
      // زر تبديل اللغة
      document.addEventListener("DOMContentLoaded", function () {
        var langBtn = document.getElementById("lang-switch-btn");
        if (langBtn) {
          langBtn.addEventListener("click", function () {
            var currentLang =
              localStorage.getItem("siteLanguage") ||
              document.documentElement.lang ||
              "ar";
            var newLang = currentLang === "ar" ? "en" : "ar";
            localStorage.setItem("siteLanguage", newLang);
            document.documentElement.lang = newLang;
            location.reload();
          });
          var currentLang =
            localStorage.getItem("siteLanguage") ||
            document.documentElement.lang ||
            "ar";
          langBtn.textContent = currentLang === "ar" ? "English" : "العربية";
        }
      });
    </script>
  </body>
</html>
