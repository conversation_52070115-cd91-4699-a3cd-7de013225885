// Service Worker لمؤسسة ركن النحت
// يوفر التخزين المؤقت وتحسين الأداء

const CACHE_NAME = 'fahdmansor-v1.0.0';
const STATIC_CACHE = 'fahdmansor-static-v1.0.0';
const DYNAMIC_CACHE = 'fahdmansor-dynamic-v1.0.0';

// الملفات الأساسية للتخزين المؤقت
const STATIC_FILES = [
    '/',
    '/index.html',
    '/sculptures.html',
    '/decorations.html',
    '/gallery.html',
    '/contact.html',
    '/manifest.json',
    '/assets/site/profile.jpg',
    'https://cdn.tailwindcss.com',
    'https://fonts.googleapis.com/css2?family=Cairo:wght@400;700;900&display=swap'
];

// تثبيت Service Worker
self.addEventListener('install', event => {
    console.log('Service Worker: Installing...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then(cache => {
                console.log('Service Worker: Caching static files');
                return cache.addAll(STATIC_FILES);
            })
            .catch(error => {
                console.error('Service Worker: Error caching static files:', error);
            })
    );
    
    // تفعيل Service Worker فوراً
    self.skipWaiting();
});

// تفعيل Service Worker
self.addEventListener('activate', event => {
    console.log('Service Worker: Activating...');
    
    event.waitUntil(
        caches.keys().then(cacheNames => {
            return Promise.all(
                cacheNames.map(cacheName => {
                    // حذف الكاشات القديمة
                    if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
                        console.log('Service Worker: Deleting old cache:', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        })
    );
    
    // السيطرة على جميع العملاء
    self.clients.claim();
});

// اعتراض طلبات الشبكة
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);
    
    // تجاهل طلبات غير HTTP/HTTPS
    if (!request.url.startsWith('http')) {
        return;
    }
    
    // استراتيجية مختلفة للملفات المختلفة
    if (isStaticAsset(request.url)) {
        // استراتيجية Cache First للملفات الثابتة
        event.respondWith(cacheFirst(request));
    } else if (isImageRequest(request.url)) {
        // استراتيجية Cache First للصور مع fallback
        event.respondWith(cacheFirstWithFallback(request));
    } else if (isAPIRequest(request.url)) {
        // استراتيجية Network First للـ API
        event.respondWith(networkFirst(request));
    } else {
        // استراتيجية Stale While Revalidate للصفحات
        event.respondWith(staleWhileRevalidate(request));
    }
});

// التحقق من نوع الملف
function isStaticAsset(url) {
    return url.includes('.css') || 
           url.includes('.js') || 
           url.includes('fonts.googleapis.com') ||
           url.includes('cdn.tailwindcss.com');
}

function isImageRequest(url) {
    return url.includes('/assets/') && 
           (url.includes('.jpg') || url.includes('.jpeg') || url.includes('.png') || url.includes('.webp'));
}

function isAPIRequest(url) {
    return url.includes('/api/') || url.includes('api.');
}

// استراتيجية Cache First
async function cacheFirst(request) {
    try {
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            const cache = await caches.open(STATIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        return networkResponse;
    } catch (error) {
        console.error('Cache First failed:', error);
        return new Response('Offline', { status: 503 });
    }
}

// استراتيجية Cache First مع Fallback للصور
async function cacheFirstWithFallback(request) {
    try {
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
            return networkResponse;
        }
        
        // إرجاع صورة افتراضية في حالة الفشل
        return await caches.match('/assets/site/profile.jpg');
    } catch (error) {
        console.error('Image cache failed:', error);
        return await caches.match('/assets/site/profile.jpg');
    }
}

// استراتيجية Network First
async function networkFirst(request) {
    try {
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        return networkResponse;
    } catch (error) {
        console.error('Network First failed, trying cache:', error);
        const cachedResponse = await caches.match(request);
        return cachedResponse || new Response('Offline', { status: 503 });
    }
}

// استراتيجية Stale While Revalidate
async function staleWhileRevalidate(request) {
    const cache = await caches.open(DYNAMIC_CACHE);
    const cachedResponse = await cache.match(request);
    
    // تحديث الكاش في الخلفية
    const fetchPromise = fetch(request).then(networkResponse => {
        if (networkResponse.ok) {
            cache.put(request, networkResponse.clone());
        }
        return networkResponse;
    }).catch(error => {
        console.error('Stale While Revalidate network failed:', error);
    });
    
    // إرجاع النسخة المحفوظة فوراً أو انتظار الشبكة
    return cachedResponse || fetchPromise;
}

// تنظيف الكاش الديناميكي عند امتلائه
self.addEventListener('message', event => {
    if (event.data && event.data.type === 'CLEAN_CACHE') {
        cleanDynamicCache();
    }
});

async function cleanDynamicCache() {
    const cache = await caches.open(DYNAMIC_CACHE);
    const keys = await cache.keys();
    
    // الاحتفاظ بآخر 50 عنصر فقط
    if (keys.length > 50) {
        const keysToDelete = keys.slice(0, keys.length - 50);
        await Promise.all(keysToDelete.map(key => cache.delete(key)));
        console.log('Service Worker: Cleaned dynamic cache');
    }
}

// معالجة الأخطاء العامة
self.addEventListener('error', event => {
    console.error('Service Worker error:', event.error);
});

self.addEventListener('unhandledrejection', event => {
    console.error('Service Worker unhandled rejection:', event.reason);
});

// إشعارات التحديث
self.addEventListener('message', event => {
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
});

// تحسين الأداء - تحميل مسبق للصفحات المهمة
self.addEventListener('message', event => {
    if (event.data && event.data.type === 'PREFETCH_PAGES') {
        const pagesToPrefetch = [
            '/sculptures.html',
            '/decorations.html',
            '/gallery.html'
        ];
        
        prefetchPages(pagesToPrefetch);
    }
});

async function prefetchPages(pages) {
    const cache = await caches.open(DYNAMIC_CACHE);
    
    for (const page of pages) {
        try {
            const response = await fetch(page);
            if (response.ok) {
                await cache.put(page, response);
                console.log('Service Worker: Prefetched', page);
            }
        } catch (error) {
            console.error('Service Worker: Failed to prefetch', page, error);
        }
    }
}

// إحصائيات الكاش
self.addEventListener('message', async event => {
    if (event.data && event.data.type === 'GET_CACHE_STATS') {
        const staticCache = await caches.open(STATIC_CACHE);
        const dynamicCache = await caches.open(DYNAMIC_CACHE);
        
        const staticKeys = await staticCache.keys();
        const dynamicKeys = await dynamicCache.keys();
        
        event.ports[0].postMessage({
            static: staticKeys.length,
            dynamic: dynamicKeys.length,
            total: staticKeys.length + dynamicKeys.length
        });
    }
});

console.log('Service Worker: Loaded successfully');

