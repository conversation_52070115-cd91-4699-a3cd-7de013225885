{"version": 2, "builds": [{"src": "**/*", "use": "@vercel/static"}], "routes": [{"src": "/(.*)", "dest": "/$1"}], "headers": [{"source": "/assets/**", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/**/*.{jpg,jpeg,png,gif,svg,webp}", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/**/*.{css,js}", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "rewrites": [{"source": "/(.*)", "destination": "/index.html"}]}