<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta
      name="google-site-verification"
      content="zriiOK4x9s5OlW5fC9HXdhUUjLcN54mf_450qAW7EN4"
    />
    <meta name="msvalidate.01" content="8A64AED84E92757DB8320E728171B195" />
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>مؤسسة ركن النحت - فهد منصور</title>
    <meta
      name="description"
      content="مؤسسة ركن النحت - فهد منصور: نحت حديث، ديكور إبداعي، تصميم مجسمات، أعمال جبس وخشب، تماثيل، فن تشكيلي، معرض أعمال، خدمات فنية في اليمن."
    />
    <meta
      name="keywords"
      content="نحت حديث, ديكور إبداعي, تصميم فني, مجسمات, جبس, خشب, تماثيل, فن تشكيلي, معرض أعمال, مؤسسة ركن النحت, فهد منصور, خدمات فنية, اليمن, أعمال فنية, تطبيق ويب, PWA, تثبيت الموقع, بدون إنترنت, خطوط عربية, خطوط Cairo, خطوط محلية, خدمة العملاء, تواصل, أعمال ديكور, CNC, تصميم داخلي, أعمال يدوية, إبداع, أصالة, ابتكار"
    />
    <meta name="author" content="فهد منصور" />

    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="مؤسسة ركن النحت - فهد منصور" />
    <meta
      name="twitter:description"
      content="اكتشف عالم الفن والإبداع مع مؤسسة ركن النحت"
    />
    <meta
      name="twitter:image"
      content="https://fahdmansor.surge.sh/assets/site/profile2.png"
    />

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="مؤسسة ركن النحت - فهد منصور" />
    <meta
      property="og:description"
      content="اكتشف عالم الفن والإبداع مع مؤسسة ركن النحت"
    />
    <meta property="og:image" content="assets/site/profile2.png" />
    <meta property="og:type" content="website" />

    <!-- Canonical Link -->
    <link rel="canonical" href="https://fahdmansor.surge.sh/" />

    <!-- Sitemap & Robots -->
    <link
      rel="sitemap"
      type="application/xml"
      title="Sitemap"
      href="sitemap.xml"
    />
    <meta name="robots" content="index, follow" />

    <!-- Structured Data (JSON-LD) -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "مؤسسة ركن النحت - فهد منصور",
        "url": "https://fahdmansor.surge.sh/",
        "logo": "https://fahdmansor.surge.sh/assets/site/profile2.png",
        "description": "مؤسسة ركن النحت متخصصة في فن النحت الحديث والديكور الإبداعي.",
        "contactPoint": [
          {
            "@type": "ContactPoint",
            "email": "<EMAIL>",
            "telephone": "776245622",
            "contactType": "customer service",
            "areaServed": "YE"
          }
        ],
        "address": {
          "@type": "PostalAddress",
          "addressCountry": "YE"
        }
      }
    </script>

    <!-- Google Analytics (gtag.js) -->
    <script
      async
      src="https://www.googletagmanager.com/gtag/js?id=G-QPXW0LPEV8"
    ></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }
      gtag("js", new Date());
      gtag("config", "G-QPXW0LPEV8");

      // تتبع الضغط على روابط الأقسام الرئيسية
      document.addEventListener("DOMContentLoaded", function () {
        var links = document.querySelectorAll(
          'a[href="sculptures.html"], a[href="decorations.html"], a[href="gallery.html"], a[href="contact.html"]'
        );
        links.forEach(function (link) {
          link.addEventListener("click", function () {
            gtag("event", "section_link_click", {
              event_category: "Navigation",
              event_label: link.getAttribute("href"),
            });
          });
        });
      });
    </script>

    <!-- أيقونات الموقع -->
    <link rel="icon" type="image/png" href="assets/site/favicon.png" />
    <link rel="manifest" href="manifest.json" />
    <meta name="theme-color" content="#f5f5f5" />

    <!-- تحسين سرعة تحميل الصور -->
    <style>
      img[loading="lazy"] {
        min-height: 50px;
        background: #eee;
      }
    </style>

    <!-- تحميل الخطوط محلياً لتثبيت الموقع كتطبيق -->
    <link
      rel="preload"
      href="assets/fonts/Cairo-Regular.woff2"
      as="font"
      type="font/woff2"
      crossorigin
    />
    <link
      rel="preload"
      href="assets/fonts/Cairo-Bold.woff2"
      as="font"
      type="font/woff2"
      crossorigin
    />
    <link rel="preload" href="assets/site/profile2.png" as="image" />

    <style>
      @font-face {
        font-family: "Cairo";
        src: url("assets/fonts/Cairo-Regular.woff2") format("woff2");
        font-weight: 400;
        font-style: normal;
        font-display: swap;
      }
      @font-face {
        font-family: "Cairo";
        src: url("assets/fonts/Cairo-Bold.woff2") format("woff2");
        font-weight: 700;
        font-style: normal;
        font-display: swap;
      }
      body {
        font-family: "Cairo", "sans-serif";
      }

      /* صور متجاوبة واحترافية */
      .responsive-img {
        max-width: 100%;
        height: auto;
        display: block;
        margin-left: auto;
        margin-right: auto;
        border-radius: 14px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.13);
        transition: transform 0.3s, box-shadow 0.3s;
        cursor: pointer;
      }
      .responsive-img:hover {
        transform: scale(1.04);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.18);
      }

      /* شبكة صور احترافية */
      .gallery-grid,
      .decorations-grid,
      .sculptures-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        gap: 18px;
      }

      /* نافذة عرض الصورة (Modal) */
      .modal-img-overlay {
        display: none;
        position: fixed;
        z-index: 10000;
        left: 0;
        top: 0;
        width: 100vw;
        height: 100vh;
        background: rgba(0, 0, 0, 0.92);
        align-items: center;
        justify-content: center;
      }
      .modal-img-overlay.active {
        display: flex;
      }
      .modal-img-content {
        max-width: 90vw;
        max-height: 80vh;
        border-radius: 1rem;
        box-shadow: 0 0 40px #000a;
        border: 4px solid #fbbf24;
      }
      .modal-img-close {
        position: absolute;
        top: 32px;
        right: 48px;
        font-size: 2.5rem;
        color: #fff;
        cursor: pointer;
        z-index: 10001;
        transition: color 0.2s;
      }
      .modal-img-close:hover {
        color: #fbbf24;
      }

      html {
        scroll-behavior: smooth;
      }
      body {
        font-family: "Cairo", "sans-serif";
      }

      /* تحسينات الأداء للصور */
      img {
        content-visibility: auto;
      }

      /* رسوم متحركة للتحميل */
      .loading-skeleton {
        background: linear-gradient(
          90deg,
          #f0f0f0 25%,
          #e0e0e0 50%,
          #f0f0f0 75%
        );
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
      }

      @keyframes loading {
        0% {
          background-position: 200% 0;
        }
        100% {
          background-position: -200% 0;
        }
      }

      /* تحسين إمكانية الوصول */
      *:focus {
        outline: 2px solid #fbbf24;
        outline-offset: 2px;
      }

      /* شريط التمرير المخصص */
      ::-webkit-scrollbar {
        width: 12px;
      }
      ::-webkit-scrollbar-track {
        background: #111827;
      }
      ::-webkit-scrollbar-thumb {
        background: linear-gradient(180deg, #fbbf24, #d97706);
        border-radius: 10px;
        border: 3px solid #111827;
      }
      ::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(180deg, #fcd34d, #ea580c);
      }

      /* رسوم متحركة للظهور */
      @keyframes fade-in {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      .animate-fade-in {
        animation: fade-in 1s ease-out;
      }

      /* تأثير النبض للأزرار */
      @keyframes pulse-glow {
        0%,
        100% {
          box-shadow: 0 6px 24px rgba(251, 191, 36, 0.3),
            0 2px 8px rgba(0, 0, 0, 0.2);
        }
        50% {
          box-shadow: 0 8px 32px rgba(251, 191, 36, 0.4),
            0 4px 12px rgba(0, 0, 0, 0.25);
        }
      }
      .animate-pulse-glow {
        animation: pulse-glow 3s ease-in-out infinite;
      }

      /* تحسين تحديد النص */
      ::selection {
        background-color: #fbbf24;
        color: #111827;
      }

      /* تحسينات للشاشات الصغيرة */
      @media (max-width: 640px) {
        .hero-title {
          font-size: 2.5rem !important;
          line-height: 1.1 !important;
        }
      }

      /* تحسينات للشاشات المتوسطة */
      @media (min-width: 641px) and (max-width: 1024px) {
        .hero-title {
          font-size: 4rem !important;
        }
      }

      /* تحسينات للشاشات الكبيرة */
      @media (min-width: 1025px) {
        .hero-title {
          font-size: 6rem !important;
        }
      }
    </style>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- نظام إدارة اللغات -->
    <script src="assets/js/translations.js"></script>
    <script src="assets/js/language-manager.js"></script>

    <script>
      tailwind.config = {
        theme: {
          extend: {
            fontFamily: {
              sans: ["Cairo", "sans-serif"],
            },
            colors: {
              "brand-dark": "#111827",
              "brand-light": "#ffffff",
              "brand-accent": {
                DEFAULT: "#F59E0B",
                start: "#FBBF24",
                end: "#D97706",
              },
            },
          },
        },
      };

      // تطبيق الكلاس الاحترافي على كل الصور تلقائياً
      document.addEventListener("DOMContentLoaded", function () {
        document
          .querySelectorAll(
            'img:not(.responsive-img):not([id^="modal"]):not([id^="lightbox"])'
          )
          .forEach(function (img) {
            img.classList.add("responsive-img");
            img.setAttribute("loading", "lazy");
          });
      });
    </script>
  </head>

  <body class="bg-brand-dark text-brand-light">
    <!-- نافذة عرض الصورة (Modal) احترافية -->
    <div
      id="modal-img-overlay"
      class="modal-img-overlay"
      style="backdrop-filter: blur(6px)"
    >
      <span id="modal-img-close" class="modal-img-close" title="إغلاق">×</span>
      <img
        id="modal-img-content"
        class="modal-img-content"
        src=""
        alt="صورة مكبرة"
        style="
          transition: transform 0.3s cubic-bezier(0.4, 2, 0.6, 1);
          box-shadow: 0 0 60px #000b;
          background: #fff;
        "
      />
    </div>

    <!-- زر الاشتراك في الإشعارات -->
    <button
      id="subscribe-btn"
      class="fixed left-4 bottom-4 z-50 bg-brand-accent text-brand-dark px-6 py-3 rounded-full font-bold text-lg shadow-lg animate-pulse-glow hover:bg-brand-accent/90 transition-all flex items-center gap-2"
      style="box-shadow: 0 8px 32px #fbbf2433, 0 4px 12px #0004"
      data-translate="btnSubscribe"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
        class="w-6 h-6"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V4a2 2 0 10-4 0v1.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
        />
      </svg>
      اشترك في الإشعارات
    </button>

    <!-- شريط التنقل -->
    <nav
      class="fixed top-0 left-0 right-0 z-50 bg-brand-dark/90 backdrop-blur-md border-b border-brand-accent/20"
    >
      <div class="container mx-auto px-4 py-3">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4 space-x-reverse">
            <div style="display: flex; align-items: center">
              <img
                src="assets/site/profile2.png"
                alt="شعار مؤسسة ركن النحت"
                class="w-16 h-16 rounded-full border-4 border-brand-accent shadow-lg bg-white"
                style="object-fit: cover"
              />
            </div>
            <h1
              class="text-xl font-bold text-brand-accent"
              data-translate="siteTitle"
            >
              مؤسسة ركن النحت
            </h1>
          </div>
          <div class="hidden md:flex items-center space-x-6 space-x-reverse">
            <a
              href="#home"
              class="text-brand-light hover:text-brand-accent transition-colors"
              data-translate="navHome"
              >الرئيسية</a
            >
            <a
              href="sculptures.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              data-translate="navSculptures"
              >قسم النحت</a
            >
            <a
              href="decorations.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              data-translate="navDecorations"
              >قسم الديكور</a
            >
            <a
              href="gallery.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              data-translate="navGallery"
              >معرض الأعمال</a
            >
            <a
              href="contact.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              data-translate="navContact"
              >تواصل معنا</a
            >
          </div>
          <!-- زر تبديل اللغة -->
          <button
            id="lang-switch-btn"
            class="ml-4 px-4 py-2 rounded-lg bg-brand-accent text-brand-dark font-bold hover:bg-brand-accent/90 transition-all"
          >
            English
          </button>
          <button id="mobile-menu-btn" class="md:hidden text-brand-light">
            <svg
              class="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 6h16M4 12h16M4 18h16"
              ></path>
            </svg>
          </button>
        </div>

        <!-- القائمة المحمولة -->
        <div id="mobile-menu" class="hidden md:hidden mt-4 pb-4">
          <div class="flex flex-col space-y-3">
            <a
              href="#home"
              class="text-brand-light hover:text-brand-accent transition-colors"
              data-translate="navHome"
              >الرئيسية</a
            >
            <a
              href="sculptures.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              data-translate="navSculptures"
              >قسم النحت</a
            >
            <a
              href="decorations.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              data-translate="navDecorations"
              >قسم الديكور</a
            >
            <a
              href="gallery.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              data-translate="navGallery"
              >معرض الأعمال</a
            >
            <a
              href="contact.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              data-translate="navContact"
              >تواصل معنا</a
            >
          </div>
        </div>
      </div>
    </nav>

    <!-- القسم الرئيسي -->
    <section
      id="home"
      class="min-h-screen flex items-center justify-center relative overflow-hidden"
    >
      <!-- خلفية الصورة -->
      <div class="absolute inset-0 z-0">
        <img
          src="assets/site/profile2.png"
          alt="خلفية"
          class="w-full h-full object-contain opacity-40 drop-shadow-xl"
          style="
            max-width: 400px;
            margin: auto;
            display: block;
            filter: blur(0.5px);
          "
        />
        <div
          class="absolute inset-0 bg-gradient-to-b from-brand-dark/70 via-brand-dark/50 to-brand-dark/70"
        ></div>
      </div>

      <!-- المحتوى الرئيسي -->
      <div class="relative z-10 text-center px-4 max-w-4xl mx-auto">
        <h1
          class="hero-title text-6xl md:text-8xl font-black mb-6 text-brand-accent animate-fade-in"
          data-translate="heroTitle"
        >
          مؤسسة ركن النحت
        </h1>
        <p
          class="text-xl md:text-2xl mb-8 text-brand-light/90 animate-fade-in"
          style="animation-delay: 0.5s"
          data-translate="heroSubtitle"
        >
          حيث يلتقي الإبداع بالدقة في كل قطعة فنية
        </p>
        <div
          class="flex flex-col sm:flex-row gap-4 justify-center items-center animate-fade-in"
          style="animation-delay: 1s"
        >
          <a
            href="sculptures.html"
            class="bg-brand-accent hover:bg-brand-accent/90 text-brand-dark px-8 py-4 rounded-lg font-bold text-lg transition-all duration-300 animate-pulse-glow btn-explore"
            data-translate="btnExploreSculptures"
          >
            🎨 اكتشف أعمال النحت
          </a>
          <a
            href="decorations.html"
            class="bg-transparent border-2 border-brand-accent text-brand-accent hover:bg-brand-accent hover:text-brand-dark px-8 py-4 rounded-lg font-bold text-lg transition-all duration-300 btn-explore"
            data-translate="btnExploreDecorations"
          >
            🏠 تصاميم الديكور
          </a>
        </div>
      </div>
    </section>

    <!-- قسم نظرة عامة -->
    <section class="py-20 bg-brand-dark">
      <div class="container mx-auto px-4">
        <div class="text-center mb-16">
          <h2
            class="text-4xl font-bold text-brand-accent mb-4"
            data-translate="overviewTitle"
          >
            نظرة عامة
          </h2>
          <p
            class="text-xl text-brand-light/80 max-w-3xl mx-auto"
            data-translate="overviewDescription"
          >
            مؤسسة ركن النحت متخصصة في فن النحت الحديث والديكور الإبداعي. نقدم
            أعمال نحت فنية متميزة وتصاميم ديكور عصرية تجمع بين الأصالة
            والابتكار.
          </p>
        </div>

        <div class="grid md:grid-cols-3 gap-8">
          <div
            class="text-center p-6 bg-brand-dark border border-brand-accent/20 rounded-lg hover:border-brand-accent/40 transition-colors"
          >
            <div class="text-4xl mb-4">🎨</div>
            <h3
              class="text-xl font-bold text-brand-accent mb-2"
              data-translate="sculptureArtTitle"
            >
              فن النحت
            </h3>
            <p
              class="text-brand-light/80"
              data-translate="sculptureArtDescription"
            >
              أعمال نحت فنية متميزة بتقنيات حديثة ولمسة إبداعية فريدة
            </p>
          </div>

          <div
            class="text-center p-6 bg-brand-dark border border-brand-accent/20 rounded-lg hover:border-brand-accent/40 transition-colors"
          >
            <div class="text-4xl mb-4">🏠</div>
            <h3
              class="text-xl font-bold text-brand-accent mb-2"
              data-translate="decorDesignTitle"
            >
              تصاميم الديكور
            </h3>
            <p
              class="text-brand-light/80"
              data-translate="decorDesignDescription"
            >
              تصاميم ديكور عصرية تجمع بين الجمال والوظيفة العملية
            </p>
          </div>

          <div
            class="text-center p-6 bg-brand-dark border border-brand-accent/20 rounded-lg hover:border-brand-accent/40 transition-colors"
          >
            <div class="text-4xl mb-4">🖼️</div>
            <h3
              class="text-xl font-bold text-brand-accent mb-2"
              data-translate="galleryTitle"
            >
              معرض الأعمال
            </h3>
            <p class="text-brand-light/80" data-translate="galleryDescription">
              مجموعة متنوعة من الأعمال الفنية والتصاميم المبتكرة
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- قسم الأقسام الرئيسية -->
    <section class="py-20 bg-gradient-to-b from-brand-dark to-gray-900">
      <div class="container mx-auto px-4">
        <div class="text-center mb-16">
          <h2
            class="text-4xl font-bold text-brand-accent mb-4"
            data-translate="mainSectionsTitle"
          >
            أقسامنا الرئيسية
          </h2>
          <p
            class="text-xl text-brand-light/80"
            data-translate="mainSectionsDescription"
          >
            اكتشف عالم الفن والإبداع من خلال أقسامنا المتخصصة
          </p>
        </div>

        <div class="grid md:grid-cols-2 gap-8">
          <a href="sculptures.html" class="group block">
            <div
              class="bg-brand-dark border border-brand-accent/20 rounded-lg overflow-hidden hover:border-brand-accent/40 transition-all duration-300 group-hover:transform group-hover:scale-105"
            >
              <div
                class="h-64 bg-gradient-to-br from-brand-accent/20 to-brand-accent/5 flex items-center justify-center"
              >
                <div class="text-6xl">🎨</div>
              </div>
              <div class="p-6">
                <h3
                  class="text-2xl font-bold text-brand-accent mb-2"
                  data-translate="modernSculptureTitle"
                >
                  قسم النحت الحديث
                </h3>
                <p
                  class="text-brand-light/80 mb-4"
                  data-translate="modernSculptureDescription"
                >
                  النحت على الخشب والجبس بتقنيات CNC حديثة، من النقوش التقليدية
                  إلى التصاميم العصرية المبتكرة
                </p>
                <span
                  class="text-brand-accent font-semibold"
                  data-translate="btnViewMore"
                >
                  عرض المزيد ←
                </span>
              </div>
            </div>
          </a>

          <a href="decorations.html" class="group block">
            <div
              class="bg-brand-dark border border-brand-accent/20 rounded-lg overflow-hidden hover:border-brand-accent/40 transition-all duration-300 group-hover:transform group-hover:scale-105"
            >
              <div
                class="h-64 bg-gradient-to-br from-brand-accent/20 to-brand-accent/5 flex items-center justify-center"
              >
                <div class="text-6xl">🏠</div>
              </div>
              <div class="p-6">
                <h3
                  class="text-2xl font-bold text-brand-accent mb-2"
                  data-translate="creativeDecorTitle"
                >
                  قسم الديكور الإبداعي
                </h3>
                <p
                  class="text-brand-light/80 mb-4"
                  data-translate="creativeDecorDescription"
                >
                  تصاميم ديكور داخلي وخارجي تجمع بين الأناقة والوظيفة، مع لمسات
                  فنية تضفي طابعاً مميزاً على المساحات
                </p>
                <span
                  class="text-brand-accent font-semibold"
                  data-translate="btnViewMore"
                >
                  عرض المزيد ←
                </span>
              </div>
            </div>
          </a>
        </div>
      </div>
    </section>

    <!-- الفوتر -->
    <footer class="bg-gray-900 py-12">
      <div class="container mx-auto px-4">
        <div class="grid md:grid-cols-3 gap-8">
          <div>
            <h3
              class="text-xl font-bold text-brand-accent mb-4"
              data-translate="aboutUsTitle"
            >
              عن مؤسسة ركن النحت
            </h3>
            <p class="text-brand-light/80" data-translate="footerDescription">
              متخصصون في فن النحت الحديث والديكور الإبداعي، نقدم خدمات فنية
              متميزة تجمع بين الأصالة والابتكار.
            </p>
          </div>

          <div>
            <h3
              class="text-xl font-bold text-brand-accent mb-4"
              data-translate="quickLinksTitle"
            >
              روابط سريعة
            </h3>
            <ul class="space-y-2">
              <li>
                <a
                  href="sculptures.html"
                  class="text-brand-light/80 hover:text-brand-accent transition-colors"
                  data-translate="navSculptures"
                  >قسم النحت</a
                >
              </li>
              <li>
                <a
                  href="decorations.html"
                  class="text-brand-light/80 hover:text-brand-accent transition-colors"
                  data-translate="navDecorations"
                  >قسم الديكور</a
                >
              </li>
              <li>
                <a
                  href="gallery.html"
                  class="text-brand-light/80 hover:text-brand-accent transition-colors"
                  data-translate="navGallery"
                  >معرض الأعمال</a
                >
              </li>
              <li>
                <a
                  href="contact.html"
                  class="text-brand-light/80 hover:text-brand-accent transition-colors"
                  data-translate="navContact"
                  >تواصل معنا</a
                >
              </li>
            </ul>
          </div>

          <div>
            <h3
              class="text-xl font-bold text-brand-accent mb-4"
              data-translate="contactInfoTitle"
            >
              معلومات التواصل
            </h3>
            <div class="space-y-2 text-brand-light/80">
              <p data-translate="phoneLabel">📞 الهاتف: 776245622</p>
              <p data-translate="emailLabel">📧 البريد: <EMAIL></p>
              <p data-translate="locationLabel">📍 الموقع: اليمن</p>
            </div>
          </div>
        </div>

        <div class="border-t border-brand-accent/20 mt-8 pt-8 text-center">
          <p class="text-brand-light/60" data-translate="footerCopyright">
            جميع الحقوق محفوظة © 2024 مؤسسة ركن النحت - فهد منصور
          </p>
        </div>
      </div>
    </footer>

    <!-- JavaScript للتفاعل -->
    <script>
      // تفعيل القائمة المحمولة
      document
        .getElementById("mobile-menu-btn")
        .addEventListener("click", function () {
          const mobileMenu = document.getElementById("mobile-menu");
          mobileMenu.classList.toggle("hidden");
        });

      // تفعيل نافذة عرض الصور
      document.addEventListener("DOMContentLoaded", function () {
        const modal = document.getElementById("modal-img-overlay");
        const modalImg = document.getElementById("modal-img-content");
        const closeBtn = document.getElementById("modal-img-close");

        // إضافة مستمع للصور
        document.querySelectorAll(".responsive-img").forEach((img) => {
          img.addEventListener("click", function () {
            modal.classList.add("active");
            modalImg.src = this.src;
            modalImg.alt = this.alt;
          });
        });

        // إغلاق النافذة
        closeBtn.addEventListener("click", function () {
          modal.classList.remove("active");
        });

        modal.addEventListener("click", function (e) {
          if (e.target === modal) {
            modal.classList.remove("active");
          }
        });
      });

      // تفعيل زر الإشعارات
      document
        .getElementById("subscribe-btn")
        .addEventListener("click", function () {
          if ("Notification" in window) {
            if (Notification.permission === "granted") {
              new Notification("تم الاشتراك بنجاح!", {
                body: "ستصلك إشعارات بكل جديد من مؤسسة ركن النحت",
                icon: "assets/site/profile2.png",
              });
              localStorage.setItem("notificationsSubscribed", "true");
            } else if (Notification.permission !== "denied") {
              Notification.requestPermission().then((permission) => {
                if (permission === "granted") {
                  new Notification("تم الاشتراك بنجاح!", {
                    body: "ستصلك إشعارات بكل جديد من مؤسسة ركن النحت",
                    icon: "assets/site/profile2.png",
                  });
                  localStorage.setItem("notificationsSubscribed", "true");
                }
              });
            }
          } else {
            alert("متصفحك لا يدعم الإشعارات");
          }
        });
    </script>
    <script>
      // زر تبديل اللغة
      document.addEventListener("DOMContentLoaded", function () {
        var langBtn = document.getElementById("lang-switch-btn");
        if (langBtn) {
          langBtn.addEventListener("click", function () {
            var currentLang = window.languageManager
              ? window.languageManager.getCurrentLanguage()
              : localStorage.getItem("siteLanguage") ||
                document.documentElement.lang ||
                "ar";
            var newLang = currentLang === "ar" ? "en" : "ar";
            if (window.languageManager) {
              window.languageManager.applyLanguage(newLang);
              langBtn.textContent = newLang === "ar" ? "English" : "العربية";
            }
          });
          // تحديث نص الزر حسب اللغة الحالية
          var currentLang = window.languageManager
            ? window.languageManager.getCurrentLanguage()
            : localStorage.getItem("siteLanguage") ||
              document.documentElement.lang ||
              "ar";
          langBtn.textContent = currentLang === "ar" ? "English" : "العربية";
        }
      });
    </script>
  </body>
</html>
