// سكريبت لتطبيق نظام الترجمة على جميع صفحات الموقع
(function () {
  "use strict";

  // إضافة نظام الترجمة لأي صفحة
  function addTranslationSystem() {
    // إضافة ملفات CSS و JS المطلوبة إذا لم تكن موجودة
    if (!document.querySelector('script[src*="translations.js"]')) {
      const translationsScript = document.createElement("script");
      translationsScript.src = "assets/js/translations.js";
      document.head.appendChild(translationsScript);
    }

    if (!document.querySelector('script[src*="language-manager.js"]')) {
      const managerScript = document.createElement("script");
      managerScript.src = "assets/js/language-manager.js";
      document.head.appendChild(managerScript);
    }

    if (!document.querySelector('link[href*="language-styles.css"]')) {
      const cssLink = document.createElement("link");
      cssLink.rel = "stylesheet";
      cssLink.href = "assets/css/language-styles.css";
      document.head.appendChild(cssLink);
    }
  }

  // إضافة خصائص الترجمة للعناصر الأساسية
  function addBasicTranslationAttributes() {
    // Navigation links
    const navLinks = {
      'a[href="index.html"], a[href="./"], a[href="#home"]': "navHome",
      'a[href="sculptures.html"]': "navSculptures",
      'a[href="decorations.html"]': "navDecorations",
      'a[href="gallery.html"]': "navGallery",
      'a[href="contact.html"]': "navContact",
    };

    Object.entries(navLinks).forEach(([selector, key]) => {
      document.querySelectorAll(selector).forEach((element) => {
        if (!element.hasAttribute("data-translate")) {
          element.setAttribute("data-translate", key);
        }
      });
    });

    // Site title
    document.querySelectorAll("h1").forEach((h1) => {
      if (
        h1.textContent.includes("مؤسسة ركن النحت") &&
        !h1.hasAttribute("data-translate")
      ) {
        h1.setAttribute("data-translate", "siteTitle");
      }
    });

    // Footer elements
    document.querySelectorAll("p").forEach((p) => {
      if (
        p.textContent.includes("جميع الحقوق محفوظة") &&
        !p.hasAttribute("data-translate")
      ) {
        p.setAttribute("data-translate", "footerCopyright");
      }
    });
  }

  // إضافة خصائص ترجمة خاصة بكل صفحة
  function addPageSpecificAttributes() {
    const currentPage =
      window.location.pathname.split("/").pop() || "index.html";

    switch (currentPage) {
      case "contact.html":
        addContactPageAttributes();
        break;
      case "sculptures.html":
        addSculpturesPageAttributes();
        break;
      case "decorations.html":
        addDecorationsPageAttributes();
        break;
      case "gallery.html":
        addGalleryPageAttributes();
        break;
      default:
        addHomePageAttributes();
        break;
    }
  }

  function addContactPageAttributes() {
    // Page title
    document.querySelectorAll("h1").forEach((h1) => {
      if (
        h1.textContent.includes("تواصل معنا") &&
        !h1.hasAttribute("data-translate")
      ) {
        h1.setAttribute("data-translate", "contactTitle");
      }
    });

    // Form elements
    const formElements = {
      'input[name="name"], input[placeholder*="الاسم"]': "contactName",
      'input[name="email"], input[placeholder*="البريد"]': "contactEmail",
      'input[name="phone"], input[placeholder*="الهاتف"]': "contactPhone",
      'textarea[name="message"], textarea[placeholder*="الرسالة"]':
        "contactMessage",
    };

    Object.entries(formElements).forEach(([selector, key]) => {
      document.querySelectorAll(selector).forEach((element) => {
        if (!element.hasAttribute("data-translate")) {
          element.setAttribute("data-translate", key);
        }
      });
    });

    // Contact info sections
    document.querySelectorAll("h2, h3").forEach((heading) => {
      if (
        heading.textContent.includes("معلومات التواصل") &&
        !heading.hasAttribute("data-translate")
      ) {
        heading.setAttribute("data-translate", "contactInfoTitle");
      }
      if (
        heading.textContent.includes("أرسل لنا رسالة") &&
        !heading.hasAttribute("data-translate")
      ) {
        heading.setAttribute("data-translate", "contactFormTitle");
      }
    });

    // Contact labels
    document.querySelectorAll("h3").forEach((h3) => {
      if (
        h3.textContent.includes("الهاتف") &&
        !h3.hasAttribute("data-translate")
      ) {
        h3.setAttribute("data-translate", "phone");
      }
      if (
        h3.textContent.includes("البريد") &&
        !h3.hasAttribute("data-translate")
      ) {
        h3.setAttribute("data-translate", "email");
      }
      if (
        h3.textContent.includes("العنوان") &&
        !h3.hasAttribute("data-translate")
      ) {
        h3.setAttribute("data-translate", "address");
      }
    });

    // Send button
    document
      .querySelectorAll('button[type="submit"], input[type="submit"]')
      .forEach((btn) => {
        if (!btn.hasAttribute("data-translate")) {
          btn.setAttribute("data-translate", "btnSendMessage");
        }
      });
  }

  function addSculpturesPageAttributes() {
    document.querySelectorAll("h1").forEach((h1) => {
      if (
        h1.textContent.includes("النحت") &&
        !h1.hasAttribute("data-translate")
      ) {
        h1.setAttribute("data-translate", "sculpturesTitle");
      }
    });
  }

  function addDecorationsPageAttributes() {
    document.querySelectorAll("h1").forEach((h1) => {
      if (
        h1.textContent.includes("الديكور") &&
        !h1.hasAttribute("data-translate")
      ) {
        h1.setAttribute("data-translate", "decorationsTitle");
      }
    });
  }

  function addGalleryPageAttributes() {
    document.querySelectorAll("h1").forEach((h1) => {
      if (
        h1.textContent.includes("المعرض") &&
        !h1.hasAttribute("data-translate")
      ) {
        h1.setAttribute("data-translate", "galleryTitle");
      }
    });
  }

  function addHomePageAttributes() {
    // Hero section
    document.querySelectorAll(".hero-title, h1").forEach((title) => {
      if (
        title.textContent.includes("مؤسسة ركن النحت") &&
        !title.hasAttribute("data-translate")
      ) {
        title.setAttribute("data-translate", "heroTitle");
      }
    });

    // Buttons
    document.querySelectorAll("a").forEach((link) => {
      if (
        link.textContent.includes("اكتشف أعمال النحت") &&
        !link.hasAttribute("data-translate")
      ) {
        link.setAttribute("data-translate", "btnExploreSculptures");
      }
      if (
        link.textContent.includes("تصاميم الديكور") &&
        !link.hasAttribute("data-translate")
      ) {
        link.setAttribute("data-translate", "btnExploreDecorations");
      }
    });
  }

  // تحديث العناوين والأوصاف
  function updatePageMetadata() {
    const currentPage =
      window.location.pathname.split("/").pop() || "index.html";

    // تحديث عنوان الصفحة
    setTimeout(() => {
      if (window.languageManager && window.translations) {
        const lang = window.languageManager.getCurrentLanguage();
        let titleKey = "siteTitle";

        switch (currentPage) {
          case "contact.html":
            titleKey = "contactTitle";
            break;
          case "sculptures.html":
            titleKey = "sculpturesTitle";
            break;
          case "decorations.html":
            titleKey = "decorationsTitle";
            break;
          case "gallery.html":
            titleKey = "galleryTitle";
            break;
        }

        const title = window.translations[lang][titleKey];
        const siteTitle = window.translations[lang].siteTitle;
        if (title && siteTitle) {
          document.title =
            currentPage === "index.html"
              ? siteTitle
              : `${title} - ${siteTitle}`;
        }
      }
    }, 500);
  }

  // إضافة أنماط CSS للترجمة
  function addTranslationStyles() {
    if (!document.querySelector("#translation-styles")) {
      const style = document.createElement("style");
      style.id = "translation-styles";
      style.textContent = `
        [data-translate] {
          transition: opacity 0.2s ease-in-out;
        }
        
        .language-transitioning [data-translate] {
          opacity: 0.7;
        }
        
        html[dir="ltr"] {
          font-family: "Inter", "Roboto", "Arial", sans-serif;
        }
        
        html[dir="rtl"] {
          font-family: "Cairo", "Tahoma", "Arial", sans-serif;
        }
      `;
      document.head.appendChild(style);
    }
  }

  // تهيئة النظام
  function initialize() {
    addTranslationSystem();
    addTranslationStyles();

    // انتظار تحميل الملفات ثم تطبيق الخصائص
    setTimeout(() => {
      addBasicTranslationAttributes();
      addPageSpecificAttributes();
      updatePageMetadata();
    }, 200);

    // مراقبة تغييرات اللغة
    document.addEventListener("languageChanged", updatePageMetadata);
  }

  // بدء التهيئة
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", initialize);
  } else {
    initialize();
  }

  // تصدير للاستخدام الخارجي
  window.ApplyTranslations = {
    addBasicTranslationAttributes,
    addPageSpecificAttributes,
    updatePageMetadata,
  };
})();
