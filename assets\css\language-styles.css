/* أنماط خاصة بتبديل اللغة */

/* زر تبديل اللغة المحسن */
#lang-toggle {
  position: fixed;
  top: 24px;
  left: 24px;
  z-index: 1000;
  padding: 10px 20px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  color: #1f2937;
  border: 2px solid #e5e7eb;
  border-radius: 25px;
  font-weight: 700;
  font-size: 14px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  user-select: none;
  display: flex;
  align-items: center;
  gap: 8px;
}

#lang-toggle:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15), 0 4px 8px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-color: #fbbf24;
}

#lang-toggle:active {
  transform: translateY(0) scale(0.98);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* أيقونة اللغة */
#lang-toggle::before {
  content: "🌐";
  font-size: 16px;
  margin-right: 4px;
}

/* تأثيرات الانتقال للنصوص */
[data-translate] {
  transition: opacity 0.2s ease-in-out;
}

.language-transitioning [data-translate] {
  opacity: 0.7;
}

/* تحسينات للاتجاه RTL/LTR */
html[dir="rtl"] {
  font-family: "Cairo", "Tahoma", "Arial", sans-serif;
}

html[dir="ltr"] {
  font-family: "Inter", "Roboto", "Arial", sans-serif;
}

/* تحسين موضع زر اللغة حسب الاتجاه */
html[dir="rtl"] #lang-toggle {
  left: 24px;
  right: auto;
}

html[dir="ltr"] #lang-toggle {
  right: 24px;
  left: auto;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 640px) {
  #lang-toggle {
    top: 16px;
    left: 16px;
    padding: 8px 16px;
    font-size: 13px;
  }

  html[dir="ltr"] #lang-toggle {
    right: 16px;
    left: auto;
  }
}

/* تأثير التحميل أثناء تبديل اللغة */
.language-loading {
  position: relative;
  overflow: hidden;
}

.language-loading::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(251, 191, 36, 0.2),
    transparent
  );
  animation: loading-sweep 1s ease-in-out;
}

@keyframes loading-sweep {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* تحسين النصوص للغة الإنجليزية */
html[dir="ltr"] .hero-title {
  font-family: "Inter", "Roboto", sans-serif;
  letter-spacing: -0.02em;
}

html[dir="ltr"] .text-xl,
html[dir="ltr"] .text-2xl {
  line-height: 1.4;
}

/* تحسين التباعد للنصوص العربية */
html[dir="rtl"] .hero-title {
  letter-spacing: 0.01em;
  line-height: 1.2;
}

/* تحسين الأزرار حسب اللغة */
html[dir="ltr"] .btn-explore {
  font-family: "Inter", "Roboto", sans-serif;
  font-weight: 600;
}

html[dir="rtl"] .btn-explore {
  font-family: "Cairo", sans-serif;
  font-weight: 700;
}

/* تحسين القوائم */
html[dir="ltr"] nav a {
  font-family: "Inter", "Roboto", sans-serif;
  font-weight: 500;
}

html[dir="rtl"] nav a {
  font-family: "Cairo", sans-serif;
  font-weight: 600;
}

/* تأثير نبضة للزر عند تغيير اللغة */
#lang-toggle.language-changed {
  animation: language-pulse 0.6s ease-out;
}

@keyframes language-pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 8px 24px rgba(251, 191, 36, 0.3);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

/* تحسين إمكانية الوصول */
#lang-toggle:focus {
  outline: 3px solid #fbbf24;
  outline-offset: 2px;
}

/* تحسين الألوان في الوضع المظلم */
@media (prefers-color-scheme: dark) {
  #lang-toggle {
    background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
    color: #f9fafb;
    border-color: #6b7280;
  }

  #lang-toggle:hover {
    background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
    border-color: #fbbf24;
  }
}

/* تحسين الرسوم المتحركة للانتقال */
.page-transition {
  transition: all 0.3s ease-in-out;
}

.page-transition.fade-out {
  opacity: 0;
  transform: translateY(10px);
}

.page-transition.fade-in {
  opacity: 1;
  transform: translateY(0);
}

/* تحسين عرض النصوص الطويلة */
html[dir="ltr"] p,
html[dir="ltr"] .text-xl {
  text-align: left;
  hyphens: auto;
}

html[dir="rtl"] p,
html[dir="rtl"] .text-xl {
  text-align: right;
  hyphens: auto;
}

/* تحسين الأيقونات حسب الاتجاه */
html[dir="ltr"] .emoji {
  margin-right: 8px;
  margin-left: 0;
}

html[dir="rtl"] .emoji {
  margin-left: 8px;
  margin-right: 0;
}
