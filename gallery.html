<script>
  // تفعيل الإشعارات تلقائياً إذا كان المستخدم مشتركاً من الصفحة الرئيسية
  document.addEventListener("DOMContentLoaded", function () {
    if (localStorage.getItem("notificationsSubscribed") === "true") {
      if ("Notification" in window && Notification.permission === "granted") {
        // إشعار ترحيبي أو أي إجراء آخر
        // new Notification("مرحباً بك مجدداً! ستصلك إشعارات بكل جديد.");
      } else if (
        "Notification" in window &&
        Notification.permission !== "denied"
      ) {
        Notification.requestPermission();
      }
    }
  });
</script>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <style>
      .responsive-img {
        max-width: 100%;
        height: auto;
        display: block;
        margin-left: auto;
        margin-right: auto;
        border-radius: 14px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.13);
        transition: transform 0.3s, box-shadow 0.3s;
        cursor: pointer;
      }
      .responsive-img:hover {
        transform: scale(1.04);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.18);
      }
    </style>
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        document
          .querySelectorAll(
            'img:not(.responsive-img):not([id^="modal"]):not([id^="lightbox"])'
          )
          .forEach(function (img) {
            img.classList.add("responsive-img");
            img.setAttribute("loading", "lazy");
          });
      });
    </script>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>معرض الأعمال - مؤسسة ركن النحت</title>
    <meta
      name="description"
      content="استكشف معرض الأعمال الفنية الكامل لمؤسسة ركن النحت"
    />
    <meta
      name="keywords"
      content="معرض أعمال, فن تشكيلي, نحت وديكور, أعمال فنية, فهد منصور, ركن النحت, معرض فني"
    />
    <meta name="author" content="فهد منصور" />

    <!-- Canonical Link -->
    <link rel="canonical" href="https://fahdmansor.surge.sh/gallery.html" />

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="معرض الأعمال - مؤسسة ركن النحت" />
    <meta
      property="og:description"
      content="استكشف معرض الأعمال الفنية الكامل لمؤسسة ركن النحت"
    />
    <meta property="og:image" content="assets/site/profile2.png" />
    <meta property="og:type" content="website" />
    <meta
      property="og:url"
      content="https://fahdmansor.surge.sh/gallery.html"
    />

    <!-- SEO Meta Tags -->
    <meta name="robots" content="index, follow" />

    <!-- أيقونات الموقع -->
    <link rel="icon" type="image/jpeg" href="assets/site/profile.jpg" />
    <link rel="manifest" href="manifest.json" />
    <meta name="theme-color" content="#FBBF24" />

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- نظام إدارة اللغات -->
    <script src="assets/js/translations.js"></script>
    <script src="assets/js/language-manager.js"></script>
    <link rel="stylesheet" href="assets/css/language-styles.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700;900&display=swap"
      rel="stylesheet"
    />

    <script>
      tailwind.config = {
        theme: {
          extend: {
            fontFamily: {
              sans: ["Cairo", "sans-serif"],
            },
            colors: {
              "brand-dark": "#111827",
              "brand-light": "#ffffff",
              "brand-accent": {
                DEFAULT: "#F59E0B",
                start: "#FBBF24",
                end: "#D97706",
              },
            },
          },
        },
      };
    </script>

    <style>
      html {
        scroll-behavior: smooth;
      }
      body {
        font-family: "Cairo", "sans-serif";
      }

      .loading-skeleton {
        background: linear-gradient(
          90deg,
          #f0f0f0 25%,
          #e0e0e0 50%,
          #f0f0f0 75%
        );
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
      }

      @keyframes loading {
        0% {
          background-position: 200% 0;
        }
        100% {
          background-position: -200% 0;
        }
      }

      .image-container {
        position: relative;
        overflow: hidden;
        border-radius: 0.5rem;
        transition: transform 0.3s ease;
        cursor: pointer;
      }

      .image-container:hover {
        transform: scale(1.05);
      }

      .image-container img {
        width: 100%;
        height: 250px;
        object-fit: cover;
        transition: opacity 0.3s ease;
      }

      .image-overlay {
        position: absolute;
        inset: 0;
        background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
        opacity: 0;
        transition: opacity 0.3s ease;
        display: flex;
        align-items: end;
        padding: 1rem;
      }

      .image-container:hover .image-overlay {
        opacity: 1;
      }

      /* Modal styles */
      .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.9);
      }

      .modal-content {
        margin: auto;
        display: block;
        max-width: 90%;
        max-height: 90%;
        margin-top: 5%;
      }

      .close {
        position: absolute;
        top: 15px;
        right: 35px;
        color: #f1f1f1;
        font-size: 40px;
        font-weight: bold;
        cursor: pointer;
      }

      .close:hover {
        color: #fbbf24;
      }

      .filter-btn {
        transition: all 0.3s ease;
      }

      .filter-btn.active {
        background-color: #f59e0b;
        color: #111827;
      }
    </style>
  </head>
  <body class="bg-brand-dark text-brand-light">
    <!-- شريط التنقل -->
    <nav
      class="fixed top-0 left-0 right-0 z-50 bg-brand-dark/90 backdrop-blur-md border-b border-brand-accent/20"
    >
      <div class="container mx-auto px-4 py-3">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4 space-x-reverse">
            <img
              src="assets/site/profile.jpg"
              alt="مؤسسة ركن النحت"
              class="w-10 h-10 rounded-full"
            />
            <h1 class="text-xl font-bold text-brand-accent">مؤسسة ركن النحت</h1>
          </div>
          <div class="hidden md:flex items-center space-x-6 space-x-reverse">
            <a
              href="index.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              >الرئيسية</a
            >
            <a
              href="sculptures.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              >قسم النحت</a
            >
            <a
              href="decorations.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              >قسم الديكور</a
            >
            <a href="gallery.html" class="text-brand-accent font-bold"
              >معرض الأعمال</a
            >
            <a
              href="contact.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              >تواصل معنا</a
            >
          </div>
          <!-- زر تبديل اللغة -->
          <button
            id="lang-switch-btn"
            class="ml-4 px-4 py-2 rounded-lg bg-brand-accent text-brand-dark font-bold hover:bg-brand-accent/90 transition-all"
          >
            English
          </button>
          <button id="mobile-menu-btn" class="md:hidden text-brand-light">
            <svg
              class="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 6h16M4 12h16M4 18h16"
              ></path>
            </svg>
          </button>
        </div>
        <!-- القائمة المحمولة -->
        <div id="mobile-menu" class="hidden md:hidden mt-4 pb-4">
          <div class="flex flex-col space-y-3">
            <a
              href="index.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              >الرئيسية</a
            >
            <a
              href="sculptures.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              >قسم النحت</a
            >
            <a
              href="decorations.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              >قسم الديكور</a
            >
            <a href="gallery.html" class="text-brand-accent font-bold"
              >معرض الأعمال</a
            >
            <a
              href="contact.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              >تواصل معنا</a
            >
          </div>
        </div>
      </div>
    </nav>

    <!-- القسم الرئيسي -->
    <section class="pt-24 pb-12 bg-gradient-to-b from-brand-dark to-gray-900">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h1 class="text-5xl font-bold text-brand-accent mb-4">
            🖼️ معرض الأعمال
          </h1>
          <p class="text-xl text-brand-light/80 max-w-3xl mx-auto">
            استكشف مجموعتنا الكاملة من الأعمال الفنية المتميزة في النحت والديكور
          </p>
        </div>
      </div>
    </section>

    <!-- قسم التصفية والبحث -->
    <section class="py-8 bg-brand-dark">
      <div class="container mx-auto px-4">
        <!-- شريط البحث -->
        <div class="max-w-md mx-auto mb-8">
          <input
            type="text"
            id="search-input"
            placeholder="البحث في المعرض..."
            class="w-full px-4 py-3 bg-brand-dark border border-brand-accent/30 rounded-lg text-brand-light placeholder-brand-light/50 focus:border-brand-accent focus:outline-none"
          />
        </div>

        <!-- أزرار التصفية -->
        <div class="flex flex-wrap justify-center gap-4 mb-8">
          <button
            class="filter-btn active px-6 py-3 bg-brand-accent text-brand-dark rounded-lg font-semibold"
            data-filter="all"
          >
            جميع الأعمال
          </button>
          <button
            class="filter-btn px-6 py-3 bg-transparent border border-brand-accent text-brand-accent rounded-lg font-semibold hover:bg-brand-accent hover:text-brand-dark"
            data-filter="sculptures"
          >
            🎨 أعمال النحت
          </button>
          <button
            class="filter-btn px-6 py-3 bg-transparent border border-brand-accent text-brand-accent rounded-lg font-semibold hover:bg-brand-accent hover:text-brand-dark"
            data-filter="decorations"
          >
            🏠 أعمال الديكور
          </button>
        </div>
      </div>
    </section>

    <!-- قسم المعرض -->
    <section class="py-16 bg-gradient-to-b from-brand-dark to-gray-900">
      <div class="container mx-auto px-4">
        <!-- شبكة الصور -->
        <div
          id="gallery-grid"
          class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6"
        >
          <!-- سيتم تحميل الصور هنا بواسطة JavaScript -->
        </div>

        <!-- زر تحميل المزيد -->
        <div class="text-center mt-12">
          <button
            id="load-more"
            class="bg-brand-accent hover:bg-brand-accent/90 text-brand-dark px-8 py-3 rounded-lg font-bold transition-colors"
          >
            تحميل المزيد
          </button>
        </div>

        <!-- رسالة عدم وجود نتائج -->
        <div id="no-results" class="hidden text-center py-16">
          <div class="text-6xl mb-4">🔍</div>
          <h3 class="text-2xl font-bold text-brand-accent mb-2">
            لا توجد نتائج
          </h3>
          <p class="text-brand-light/80">
            جرب البحث بكلمات مختلفة أو اختر فئة أخرى
          </p>
        </div>
      </div>
    </section>

    <!-- Modal لعرض الصور -->
    <div id="imageModal" class="modal">
      <span class="close">&times;</span>
      <img class="modal-content" id="modalImage" />
      <div id="caption" class="text-center text-white mt-4 text-xl"></div>
    </div>

    <!-- التذييل -->
    <footer class="bg-black text-brand-light py-12">
      <div class="container mx-auto px-4">
        <div class="grid md:grid-cols-3 gap-8">
          <div>
            <h3 class="text-xl font-bold text-brand-accent mb-4">
              مؤسسة ركن النحت
            </h3>
            <p class="text-brand-light/80">
              متخصصون في فن النحت الحديث والديكور الإبداعي، نقدم أعمال فنية
              متميزة تجمع بين الأصالة والابتكار.
            </p>
          </div>

          <div>
            <h3 class="text-xl font-bold text-brand-accent mb-4">
              روابط سريعة
            </h3>
            <ul class="space-y-2">
              <li>
                <a
                  href="index.html"
                  class="text-brand-light/80 hover:text-brand-accent transition-colors"
                  >الرئيسية</a
                >
              </li>
              <li>
                <a
                  href="sculptures.html"
                  class="text-brand-light/80 hover:text-brand-accent transition-colors"
                  >قسم النحت</a
                >
              </li>
              <li>
                <a
                  href="decorations.html"
                  class="text-brand-light/80 hover:text-brand-accent transition-colors"
                  >قسم الديكور</a
                >
              </li>
              <li>
                <a href="gallery.html" class="text-brand-accent"
                  >معرض الأعمال</a
                >
              </li>
              <li>
                <a
                  href="contact.html"
                  class="text-brand-light/80 hover:text-brand-accent transition-colors"
                  >تواصل معنا</a
                >
              </li>
            </ul>
          </div>

          <div>
            <h3 class="text-xl font-bold text-brand-accent mb-4">
              معلومات التواصل
            </h3>
            <div class="space-y-2 text-brand-light/80">
              <p>📧 <EMAIL></p>
              <p>📱 776245622</p>
              <p>📍 اليمن</p>
            </div>
          </div>
        </div>

        <div class="border-t border-brand-accent/20 mt-8 pt-8 text-center">
          <p class="text-brand-light/60">
            © 2024 مؤسسة ركن النحت - فهد منصور. جميع الحقوق محفوظة.
          </p>
        </div>
      </div>
    </footer>

    <script>
      // قوائم الصور من الملفات الفعلية
      const sculptureImages = [
        "aih.jpg",
        "aih2.jpg",
        "aih3.jpg",
        "alhmady.jpg",
        "atho.jpg",
        "azbiry.jpg",
        "bbb.jpg",
        "eelam.jpg",
        "fal.jpg",
        "hash.jpg",
        "hodyfa.jpg",
        "hza.jpg",
        "mabr.jpg",
        "mabr2.jpg",
        "mabr3.jpg",
        "mgasm.jpg",
        "mgsm.jpg",
        "n1.jpg",
        "n10.jpg",
        "n11.jpg",
        "n12.jpg",
        "n13.jpg",
        "n14.jpg",
        "n15.jpg",
        "n16.jpg",
        "n17.jpg",
        "n18.jpg",
        "n19.jpg",
        "n2.jpg",
        "n20.jpg",
        "n21.jpg",
        "n22.jpg",
        "n23.jpg",
        "n24.jpg",
        "n25.jpg",
        "n26.jpg",
        "n27.jpg",
        "n28.jpg",
        "n29.jpg",
        "n3.jpg",
        "n4.jpg",
        "n5.jpg",
        "n6.jpg",
        "n7.jpg",
        "n8.jpg",
        "n9.jpg",
        "rr (1).png",
        "rr (10).jpg",
        "rr (11).jpg",
        "rr (12).jpg",
        "rr (13).jpg",
        "rr (14).jpg",
        "rr (16).jpg",
        "rr (17).jpg",
        "rr (18).jpg",
        "rr (19).jpg",
        "rr (2).jpg",
        "rr (20).jpg",
        "rr (3).jpg",
        "rr (4).jpg",
        "rr (51).png",
        "rr (52).png",
        "rr (53).jpg",
        "rr (6).jpg",
        "rr (7).jpg",
        "rr (8).jpg",
        "rr (9).jpg",
        "rr(21).png",
        "rr(22).jpg",
        "rr(5).jpg",
        "rr(50).jpg",
        "sabr.jpg",
        "twakl.jpg",
      ];
      const decorationImages = [
        "asa.jpg",
        "back.jpg",
        "cust.jpg",
        "d1.jpg",
        "d10.jpg",
        "d11.jpg",
        "d12.jpg",
        "d13.jpg",
        "d14.jpg",
        "d15.jpg",
        "d16.jpg",
        "d17.jpg",
        "d18.jpg",
        "d19.jpg",
        "d2.jpg",
        "d20.jpg",
        "d21.jpg",
        "d22.jpg",
        "d23.jpg",
        "d24.jpg",
        "d25.jpg",
        "d26.jpg",
        "d27.jpg",
        "d28.jpg",
        "d29.jpg",
        "d3.jpg",
        "d30.jpg",
        "d31.jpg",
        "d32.jpg",
        "d33.jpg",
        "d34.jpg",
        "d35.jpg",
        "d36.jpg",
        "d4.jpg",
        "d5.jpg",
        "d6.jpg",
        "d7.jpg",
        "d8.jpg",
        "d9.jpg",
        "deecor.jpg",
        "dek.jpg",
        "dicor.jpg",
        "gbs.jpg",
        "qqw.jpg",
        "school.jpg",
        "school2.jpg",
        "shop1.jpg",
        "shop2.jpg",
        "shop3.jpg",
        "shop4.jpg",
        "shop5.jpg",
        "shop6.jpg",
        "wall.jpg",
        "wall2.jpg",
        "wall5.jpg",
        "wall6.jpg",
        "wall7.jpg",
        "wws.jpg",
      ];
      // دمج جميع الصور
      const allImages = [
        ...sculptureImages.map((img) => ({
          src: `assets/sculptures/${img}`,
          type: "sculptures",
          name: img,
        })),
        ...decorationImages.map((img) => ({
          src: `assets/decorations/${img}`,
          type: "decorations",
          name: img,
        })),
      ];

      let currentPage = 0;
      const imagesPerPage = 20;
      let filteredImages = [...allImages];
      let currentFilter = "all";

      // تفعيل القائمة المحمولة
      document
        .getElementById("mobile-menu-btn")
        .addEventListener("click", function () {
          const mobileMenu = document.getElementById("mobile-menu");
          mobileMenu.classList.toggle("hidden");
        });

      // وظيفة تحميل الصور
      function loadImages() {
        const grid = document.getElementById("gallery-grid");
        const startIndex = currentPage * imagesPerPage;
        const endIndex = Math.min(
          startIndex + imagesPerPage,
          filteredImages.length
        );

        for (let i = startIndex; i < endIndex; i++) {
          const image = filteredImages[i];
          const imageDiv = document.createElement("div");
          imageDiv.className = "image-container";
          imageDiv.innerHTML = `
                    <img src="${image.src}" alt="${
            image.type === "sculptures" ? "عمل نحت" : "تصميم ديكور"
          }" 
                         class="loading-skeleton" loading="lazy"
                         onerror="this.style.display='none'"
                         onclick="openModal('${image.src}', '${
            image.type === "sculptures" ? "عمل نحت" : "تصميم ديكور"
          }')">
                    <div class="image-overlay">
                        <div class="text-white">
                            <h3 class="font-bold">${
                              image.type === "sculptures"
                                ? "🎨 عمل نحت"
                                : "🏠 تصميم ديكور"
                            }</h3>
                            <p class="text-sm opacity-80">${image.name}</p>
                        </div>
                    </div>
                `;
          grid.appendChild(imageDiv);
        }

        currentPage++;

        // إخفاء زر "تحميل المزيد" إذا تم تحميل جميع الصور
        if (endIndex >= filteredImages.length) {
          document.getElementById("load-more").style.display = "none";
        }

        // إظهار رسالة عدم وجود نتائج
        if (filteredImages.length === 0) {
          document.getElementById("no-results").classList.remove("hidden");
        } else {
          document.getElementById("no-results").classList.add("hidden");
        }
      }

      // وظيفة التصفية
      function filterImages(filter, searchTerm = "") {
        let images = [];
        if (filter === "all") {
          images = [...allImages];
        } else if (filter === "sculptures") {
          images = allImages.filter((image) => image.type === "sculptures");
        } else if (filter === "decorations") {
          images = allImages.filter((image) => image.type === "decorations");
        }
        // تصفية حسب البحث
        if (searchTerm) {
          images = images.filter((image) =>
            image.name.toLowerCase().includes(searchTerm.toLowerCase())
          );
        }
        filteredImages = images;
        // إعادة تعيين الشبكة
        document.getElementById("gallery-grid").innerHTML = "";
        currentPage = 0;
        document.getElementById("load-more").style.display =
          filteredImages.length > 0 ? "block" : "none";
        loadImages();
      }

      // تفعيل أزرار التصفية
      document.querySelectorAll(".filter-btn").forEach((btn) => {
        btn.addEventListener("click", function () {
          // إزالة الكلاس النشط من جميع الأزرار
          document
            .querySelectorAll(".filter-btn")
            .forEach((b) => b.classList.remove("active"));
          // إضافة الكلاس النشط للزر المضغوط
          this.classList.add("active");

          currentFilter = this.dataset.filter;
          const searchTerm = document.getElementById("search-input").value;
          filterImages(currentFilter, searchTerm);
        });
      });

      // تفعيل البحث
      document
        .getElementById("search-input")
        .addEventListener("input", function (e) {
          filterImages(currentFilter, e.target.value);
        });

      // تفعيل زر "تحميل المزيد"
      document
        .getElementById("load-more")
        .addEventListener("click", loadImages);

      // وظائف Modal
      // نافذة عرض الصور الاحترافية
      function openModal(src, caption) {
        const modal = document.getElementById("imageModal");
        const modalImg = document.getElementById("modalImage");
        const captionText = document.getElementById("caption");
        modal.style.display = "block";
        modalImg.src = src;
        captionText.innerHTML = caption;
        setTimeout(() => {
          modalImg.style.transform = "scale(1.08) rotate(-1deg)";
        }, 10);
      }

      // إغلاق Modal
      document.querySelector(".close").addEventListener("click", function () {
        document.getElementById("imageModal").style.display = "none";
        document.getElementById("modalImage").style.transform = "scale(0.95)";
      });

      // إغلاق Modal عند النقر خارجه
      window.addEventListener("click", function (event) {
        const modal = document.getElementById("imageModal");
        if (event.target === modal) {
          modal.style.display = "none";
          document.getElementById("modalImage").style.transform = "scale(0.95)";
        }
      });

      // تحميل الصور الأولى عند تحميل الصفحة
      document.addEventListener("DOMContentLoaded", function () {
        loadImages();
      });

      // تسجيل Service Worker
      if ("serviceWorker" in navigator) {
        window.addEventListener("load", function () {
          navigator.serviceWorker
            .register("sw.js")
            .then(function (registration) {
              // Service Worker registered successfully
            });
        });
      }
    </script>
    <script>
      // زر تبديل اللغة
      document.addEventListener("DOMContentLoaded", function () {
        var langBtn = document.getElementById("lang-switch-btn");
        if (langBtn) {
          langBtn.addEventListener("click", function () {
            var currentLang =
              localStorage.getItem("siteLanguage") ||
              document.documentElement.lang ||
              "ar";
            var newLang = currentLang === "ar" ? "en" : "ar";
            localStorage.setItem("siteLanguage", newLang);
            document.documentElement.lang = newLang;
            if (
              window.languageManager &&
              typeof window.languageManager.applyLanguage === "function"
            ) {
              window.languageManager.applyLanguage(newLang);
            }
            // لا حاجة لإعادة تحميل الصفحة
          });
          var currentLang =
            localStorage.getItem("siteLanguage") ||
            document.documentElement.lang ||
            "ar";
          langBtn.textContent = currentLang === "ar" ? "English" : "العربية";
        }
      });
    </script>

    <!-- تطبيق نظام الترجمة -->
    <script src="assets/js/apply-translations.js"></script>
  </body>
</html>
