<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>دليل تطبيق نظام الترجمة</title>
    <style>
      body {
        font-family: "Cairo", Arial, sans-serif;
        line-height: 1.6;
        margin: 0;
        padding: 20px;
        background: #f5f5f5;
      }
      .container {
        max-width: 1200px;
        margin: 0 auto;
        background: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      h1,
      h2,
      h3 {
        color: #2c3e50;
      }
      .code-block {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 5px;
        padding: 15px;
        margin: 10px 0;
        overflow-x: auto;
        direction: ltr;
        text-align: left;
      }
      .step {
        background: #e8f5e8;
        border-left: 4px solid #28a745;
        padding: 15px;
        margin: 15px 0;
      }
      .warning {
        background: #fff3cd;
        border-left: 4px solid #ffc107;
        padding: 15px;
        margin: 15px 0;
      }
      .success {
        background: #d4edda;
        border-left: 4px solid #28a745;
        padding: 15px;
        margin: 15px 0;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>🌐 دليل تطبيق نظام الترجمة الشامل</h1>

      <div class="success">
        <h3>✅ تم إنشاء نظام ترجمة متقدم يدعم:</h3>
        <ul>
          <li>تبديل سلس بين العربية والإنجليزية</li>
          <li>حفظ اختيار اللغة في المتصفح</li>
          <li>تحديث تلقائي لجميع النصوص</li>
          <li>دعم RTL/LTR</li>
          <li>تحديث Meta Tags للـ SEO</li>
        </ul>
      </div>

      <h2>📁 الملفات التي تم إنشاؤها:</h2>

      <div class="step">
        <h3>1. ملف الترجمات الأساسي</h3>
        <p><strong>المسار:</strong> <code>assets/js/translations.js</code></p>
        <p>يحتوي على جميع النصوص باللغتين العربية والإنجليزية</p>
      </div>

      <div class="step">
        <h3>2. مدير اللغات</h3>
        <p>
          <strong>المسار:</strong> <code>assets/js/language-manager.js</code>
        </p>
        <p>يدير تبديل اللغات وحفظ الاختيارات</p>
      </div>

      <div class="step">
        <h3>3. أنماط اللغات</h3>
        <p>
          <strong>المسار:</strong> <code>assets/css/language-styles.css</code>
        </p>
        <p>أنماط CSS خاصة بتبديل اللغات والانتقالات</p>
      </div>

      <div class="step">
        <h3>4. سكريبت التطبيق التلقائي</h3>
        <p>
          <strong>المسار:</strong> <code>assets/js/apply-translations.js</code>
        </p>
        <p>يطبق نظام الترجمة تلقائياً على أي صفحة</p>
      </div>

      <h2>🔧 كيفية تطبيق النظام على الصفحات الموجودة:</h2>

      <div class="step">
        <h3>الطريقة الأولى: إضافة الملفات يدوياً</h3>
        <p>أضف هذا الكود في <code>&lt;head&gt;</code> لكل صفحة:</p>
        <div class="code-block">
          &lt;!-- نظام إدارة اللغات --&gt; &lt;script
          src="assets/js/translations.js"&gt;&lt;/script&gt; &lt;script
          src="assets/js/language-manager.js"&gt;&lt;/script&gt; &lt;link
          rel="stylesheet" href="assets/css/language-styles.css" /&gt;
        </div>
      </div>

      <div class="step">
        <h3>الطريقة الثانية: التطبيق التلقائي</h3>
        <p>أضف هذا السطر فقط في نهاية <code>&lt;body&gt;</code>:</p>
        <div class="code-block">
          &lt;script src="assets/js/apply-translations.js"&gt;&lt;/script&gt;
        </div>
        <p>سيقوم بإضافة جميع الملفات المطلوبة تلقائياً!</p>
      </div>

      <h2>🏷️ إضافة خصائص الترجمة للعناصر:</h2>

      <div class="step">
        <h3>للنصوص العادية:</h3>
        <div class="code-block">
          &lt;h1 data-translate="siteTitle"&gt;مؤسسة ركن النحت&lt;/h1&gt; &lt;p
          data-translate="heroDescription"&gt;وصف الموقع&lt;/p&gt;
        </div>
      </div>

      <div class="step">
        <h3>لحقول النماذج:</h3>
        <div class="code-block">
          &lt;input type="text" data-translate="contactName" placeholder="الاسم"
          /&gt; &lt;textarea data-translate="contactMessage"
          placeholder="الرسالة"&gt;&lt;/textarea&gt;
        </div>
      </div>

      <div class="step">
        <h3>للروابط والأزرار:</h3>
        <div class="code-block">
          &lt;a href="contact.html" data-translate="navContact"&gt;تواصل
          معنا&lt;/a&gt; &lt;button data-translate="btnSendMessage"&gt;إرسال
          الرسالة&lt;/button&gt;
        </div>
      </div>

      <h2>🎯 مفاتيح الترجمة المتاحة:</h2>

      <div class="step">
        <h3>التنقل:</h3>
        <ul>
          <li><code>navHome</code> - الرئيسية</li>
          <li><code>navSculptures</code> - قسم النحت</li>
          <li><code>navDecorations</code> - قسم الديكور</li>
          <li><code>navGallery</code> - معرض الأعمال</li>
          <li><code>navContact</code> - تواصل معنا</li>
        </ul>
      </div>

      <div class="step">
        <h3>الصفحة الرئيسية:</h3>
        <ul>
          <li><code>heroTitle</code> - العنوان الرئيسي</li>
          <li><code>heroSubtitle</code> - العنوان الفرعي</li>
          <li><code>btnExploreSculptures</code> - زر استكشاف النحت</li>
          <li><code>btnExploreDecorations</code> - زر استكشاف الديكور</li>
        </ul>
      </div>

      <div class="step">
        <h3>صفحة التواصل:</h3>
        <ul>
          <li><code>contactTitle</code> - عنوان الصفحة</li>
          <li><code>contactName</code> - حقل الاسم</li>
          <li><code>contactEmail</code> - حقل البريد</li>
          <li><code>contactPhone</code> - حقل الهاتف</li>
          <li><code>contactMessage</code> - حقل الرسالة</li>
          <li><code>btnSendMessage</code> - زر الإرسال</li>
        </ul>
      </div>

      <h2>🚀 تطبيق النظام على الصفحات الحالية:</h2>

      <div class="warning">
        <h3>⚠️ ملاحظة مهمة:</h3>
        <p>تم إنشاء نسخ محدثة من الصفحات:</p>
        <ul>
          <li><code>index-updated.html</code> - الصفحة الرئيسية المحدثة</li>
          <li><code>contact-updated.html</code> - صفحة التواصل المحدثة</li>
        </ul>
        <p>يمكنك استبدال الملفات الأصلية بهذه النسخ أو نسخ الكود منها.</p>
      </div>

      <div class="step">
        <h3>لتطبيق النظام على باقي الصفحات:</h3>
        <ol>
          <li>
            افتح كل صفحة (sculptures.html, decorations.html, gallery.html)
          </li>
          <li>أضف هذا السطر قبل إغلاق <code>&lt;/body&gt;</code>:</li>
          <div class="code-block">
            &lt;script src="assets/js/apply-translations.js"&gt;&lt;/script&gt;
          </div>
          <li>احفظ الملف</li>
          <li>سيتم تطبيق النظام تلقائياً!</li>
        </ol>
      </div>

      <h2>🎨 ميزات إضافية:</h2>

      <div class="success">
        <h3>✨ ما يتم تطبيقه تلقائياً:</h3>
        <ul>
          <li>زر تبديل اللغة في أعلى يسار الصفحة</li>
          <li>حفظ اختيار اللغة في المتصفح</li>
          <li>تحديث اتجاه النص (RTL/LTR)</li>
          <li>تحديث عنوان الصفحة</li>
          <li>تحديث Meta Tags للـ SEO</li>
          <li>تأثيرات انتقالية سلسة</li>
        </ul>
      </div>

      <div class="step">
        <h3>🔧 تخصيص إضافي:</h3>
        <p>
          يمكنك إضافة ترجمات جديدة في ملف
          <code>translations.js</code> واستخدامها مباشرة:
        </p>
        <div class="code-block">
          // في ملف translations.js ar: { myNewText: "النص الجديد بالعربية" },
          en: { myNewText: "New text in English" } // في HTML &lt;p
          data-translate="myNewText"&gt;النص الجديد بالعربية&lt;/p&gt;
        </div>
      </div>

      <h2>📱 اختبار النظام:</h2>

      <div class="step">
        <ol>
          <li>افتح أي صفحة في المتصفح</li>
          <li>ابحث عن زر تبديل اللغة في أعلى يسار الصفحة</li>
          <li>اضغط على الزر لتبديل اللغة</li>
          <li>تأكد من تحديث جميع النصوص</li>
          <li>أعد تحميل الصفحة للتأكد من حفظ الاختيار</li>
        </ol>
      </div>

      <div class="success">
        <h3>🎉 تهانينا!</h3>
        <p>تم إنشاء نظام ترجمة متقدم وشامل لموقعك. النظام يدعم:</p>
        <ul>
          <li>✅ تبديل سلس بين العربية والإنجليزية</li>
          <li>✅ حفظ تلقائي لاختيار اللغة</li>
          <li>✅ تحديث شامل لجميع عناصر الصفحة</li>
          <li>✅ دعم كامل للـ SEO</li>
          <li>✅ تصميم متجاوب ومتوافق مع جميع الأجهزة</li>
        </ul>
      </div>
    </div>
  </body>
</html>
