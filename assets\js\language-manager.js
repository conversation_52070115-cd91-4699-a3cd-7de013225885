// مدير اللغات المحسن
class LanguageManager {
  constructor() {
    this.currentLang = this.getSavedLanguage() || "ar";
    this.translations = window.translations || {};
    this.init();
  }

  // تهيئة مدير اللغات
  init() {
    this.applyLanguage(this.currentLang);
    this.setupLanguageToggle();
    this.setupLanguageAttributes();
  }

  // الحصول على اللغة المحفوظة
  getSavedLanguage() {
    return localStorage.getItem("preferred-language");
  }

  // حفظ اللغة المختارة
  saveLanguage(lang) {
    localStorage.setItem("preferred-language", lang);
  }

  // تطبيق اللغة
  applyLanguage(lang) {
    if (!this.translations[lang]) {
      console.warn(`Language ${lang} not found, falling back to Arabic`);
      lang = "ar";
    }

    // إضافة تأثير التحميل
    document.body.classList.add("language-transitioning");

    this.currentLang = lang;
    this.saveLanguage(lang);

    // تحديث خصائص HTML
    document.documentElement.lang = lang;
    document.documentElement.dir = lang === "ar" ? "rtl" : "ltr";

    // تحديث العنوان والوصف
    document.title = this.translations[lang].siteTitle;
    this.updateMetaTag("description", this.translations[lang].siteDescription);

    // تحديث Open Graph tags
    this.updateMetaTag(
      "og:title",
      this.translations[lang].siteTitle,
      "property"
    );
    this.updateMetaTag(
      "og:description",
      this.translations[lang].siteDescription,
      "property"
    );

    // تحديث Twitter Card tags
    this.updateMetaTag(
      "twitter:title",
      this.translations[lang].siteTitle,
      "name"
    );
    this.updateMetaTag(
      "twitter:description",
      this.translations[lang].siteDescription,
      "name"
    );

    // تحديث جميع العناصر المترجمة مع تأثير انتقالي
    setTimeout(() => {
      this.updateTranslatedElements();

      // تحديث زر تبديل اللغة
      this.updateLanguageToggle();

      // إزالة تأثير التحميل
      document.body.classList.remove("language-transitioning");

      // إضافة تأثير النبضة للزر
      const toggleBtn = document.getElementById("lang-toggle");
      if (toggleBtn) {
        toggleBtn.classList.add("language-changed");
        setTimeout(() => {
          toggleBtn.classList.remove("language-changed");
        }, 600);
      }

      // إطلاق حدث تغيير اللغة
      this.dispatchLanguageChangeEvent();
    }, 150);
  }

  // تحديث meta tags
  updateMetaTag(name, content, attribute = "name") {
    const metaTag = document.querySelector(`meta[${attribute}="${name}"]`);
    if (metaTag) {
      metaTag.setAttribute("content", content);
    }
  }

  // تحديث العناصر المترجمة
  updateTranslatedElements() {
    const elements = document.querySelectorAll("[data-translate]");
    elements.forEach((element) => {
      const key = element.getAttribute("data-translate");
      const translation = this.getTranslation(key);

      if (translation) {
        if (element.tagName === "INPUT" || element.tagName === "TEXTAREA") {
          element.placeholder = translation;
        } else {
          element.textContent = translation;
        }
      }
    });
  }

  // الحصول على ترجمة بالمفتاح
  getTranslation(key) {
    const keys = key.split(".");
    let translation = this.translations[this.currentLang];

    for (const k of keys) {
      if (translation && translation[k]) {
        translation = translation[k];
      } else {
        return null;
      }
    }

    return translation;
  }

  // إعداد زر تبديل اللغة
  setupLanguageToggle() {
    let toggleBtn = document.getElementById("lang-toggle");

    if (!toggleBtn) {
      // إنشاء زر تبديل اللغة إذا لم يكن موجوداً
      toggleBtn = this.createLanguageToggle();
      document.body.appendChild(toggleBtn);
    }

    toggleBtn.addEventListener("click", () => {
      this.toggleLanguage();
    });
  }

  // إنشاء زر تبديل اللغة
  createLanguageToggle() {
    const button = document.createElement("button");
    button.id = "lang-toggle";
    button.className =
      "fixed top-6 left-6 z-50 px-4 py-2 bg-white text-gray-800 rounded-full font-bold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105";
    button.style.cssText = `
      position: fixed;
      top: 24px;
      left: 24px;
      z-index: 1000;
      padding: 8px 18px;
      background: #fff;
      color: #222;
      border-radius: 24px;
      font-weight: bold;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      cursor: pointer;
      border: none;
      transition: all 0.3s ease;
    `;

    // إضافة تأثيرات hover
    button.addEventListener("mouseenter", () => {
      button.style.transform = "scale(1.05)";
      button.style.boxShadow = "0 4px 16px rgba(0,0,0,0.15)";
    });

    button.addEventListener("mouseleave", () => {
      button.style.transform = "scale(1)";
      button.style.boxShadow = "0 2px 8px rgba(0,0,0,0.1)";
    });

    return button;
  }

  // تحديث نص زر تبديل اللغة
  updateLanguageToggle() {
    const toggleBtn = document.getElementById("lang-toggle");
    if (toggleBtn) {
      toggleBtn.textContent = this.currentLang === "ar" ? "English" : "العربية";
    }
  }

  // تبديل اللغة
  toggleLanguage() {
    const newLang = this.currentLang === "ar" ? "en" : "ar";
    this.applyLanguage(newLang);
  }

  // إعداد خصائص اللغة للعناصر
  setupLanguageAttributes() {
    // إضافة خصائص اللغة للعناصر التي تحتاجها
    const elementsToTranslate = {
      // Navigation
      'nav a[href="index.html"], nav a[href="./"]': "navHome",
      'nav a[href="sculptures.html"]': "navSculptures",
      'nav a[href="decorations.html"]': "navDecorations",
      'nav a[href="gallery.html"]': "navGallery",
      'nav a[href="contact.html"]': "navContact",

      // Common buttons
      ".btn-explore": "btnExplore",
      ".btn-contact": "btnContact",
      ".btn-view-more": "btnViewMore",
      ".btn-send": "btnSendMessage",
      ".btn-close": "btnClose",

      // Form elements
      'input[name="name"]': "contactName",
      'input[name="email"]': "contactEmail",
      'input[name="phone"]': "contactPhone",
      'textarea[name="message"]': "contactMessage",
    };

    Object.entries(elementsToTranslate).forEach(([selector, key]) => {
      const elements = document.querySelectorAll(selector);
      elements.forEach((element) => {
        if (!element.hasAttribute("data-translate")) {
          element.setAttribute("data-translate", key);
        }
      });
    });
  }

  // إطلاق حدث تغيير اللغة
  dispatchLanguageChangeEvent() {
    const event = new CustomEvent("languageChanged", {
      detail: { language: this.currentLang },
    });
    document.dispatchEvent(event);
  }

  // الحصول على اللغة الحالية
  getCurrentLanguage() {
    return this.currentLang;
  }

  // التحقق من كون اللغة عربية
  isRTL() {
    return this.currentLang === "ar";
  }
}

// تهيئة مدير اللغات عند تحميل الصفحة
document.addEventListener("DOMContentLoaded", () => {
  window.languageManager = new LanguageManager();
});

// تصدير الكلاس للاستخدام في ملفات أخرى
if (typeof module !== "undefined" && module.exports) {
  module.exports = LanguageManager;
}
