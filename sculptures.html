<script>
  // تفعيل الإشعارات تلقائياً إذا كان المستخدم مشتركاً من الصفحة الرئيسية
  document.addEventListener("DOMContentLoaded", function () {
    if (localStorage.getItem("notificationsSubscribed") === "true") {
      if ("Notification" in window && Notification.permission === "granted") {
        // إشعار ترحيبي أو أي إجراء آخر
        // new Notification("مرحباً بك مجدداً! ستصلك إشعارات بكل جديد.");
      } else if (
        "Notification" in window &&
        Notification.permission !== "denied"
      ) {
        Notification.requestPermission();
      }
    }
  });
</script>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <style>
      .responsive-img {
        max-width: 100%;
        height: auto;
        display: block;
        margin-left: auto;
        margin-right: auto;
        border-radius: 14px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.13);
        transition: transform 0.3s, box-shadow 0.3s;
        cursor: pointer;
      }
      .responsive-img:hover {
        transform: scale(1.04);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.18);
      }
    </style>
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        document
          .querySelectorAll(
            'img:not(.responsive-img):not([id^="modal"]):not([id^="lightbox"])'
          )
          .forEach(function (img) {
            img.classList.add("responsive-img");
            img.setAttribute("loading", "lazy");
          });
      });
    </script>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>قسم النحت - مؤسسة ركن النحت</title>
    <meta
      name="description"
      content="اكتشف أعمال النحت الفنية المتميزة في مؤسسة ركن النحت"
    />
    <meta
      name="keywords"
      content="نحت, تماثيل, فن تشكيلي, مجسمات, أعمال فنية, فهد منصور, ركن النحت"
    />
    <meta name="author" content="فهد منصور" />

    <!-- Canonical Link -->
    <link rel="canonical" href="https://fahdmansor.surge.sh/sculptures.html" />

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="قسم النحت - مؤسسة ركن النحت" />
    <meta
      property="og:description"
      content="اكتشف أعمال النحت الفنية المتميزة في مؤسسة ركن النحت"
    />
    <meta property="og:image" content="assets/site/profile2.png" />
    <meta property="og:type" content="website" />
    <meta
      property="og:url"
      content="https://fahdmansor.surge.sh/sculptures.html"
    />

    <!-- SEO Meta Tags -->
    <meta name="robots" content="index, follow" />

    <!-- أيقونات الموقع -->
    <link rel="icon" type="image/jpeg" href="assets/site/profile.jpg" />
    <link rel="manifest" href="manifest.json" />
    <meta name="theme-color" content="#FBBF24" />

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- نظام إدارة اللغات -->
    <script src="assets/js/translations.js"></script>
    <script src="assets/js/language-manager.js"></script>
    <link rel="stylesheet" href="assets/css/language-styles.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700;900&display=swap"
      rel="stylesheet"
    />

    <script>
      tailwind.config = {
        theme: {
          extend: {
            fontFamily: {
              sans: ["Cairo", "sans-serif"],
            },
            colors: {
              "brand-dark": "#111827",
              "brand-light": "#ffffff",
              "brand-accent": {
                DEFAULT: "#F59E0B",
                start: "#FBBF24",
                end: "#D97706",
              },
            },
          },
        },
      };
    </script>

    <style>
      /* نافذة عرض الصورة (Lightbox) */
      .lightbox-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: rgba(0, 0, 0, 0.85);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        transition: opacity 0.3s;
      }
      .lightbox-img {
        max-width: 90vw;
        max-height: 80vh;
        border-radius: 1rem;
        box-shadow: 0 0 40px #000a;
        border: 4px solid #fbbf24;
      }
      .lightbox-overlay[hidden] {
        display: none;
      }
      .lightbox-close {
        position: absolute;
        top: 32px;
        right: 48px;
        font-size: 2.5rem;
        color: #fff;
        cursor: pointer;
        z-index: 10001;
        transition: color 0.2s;
      }
      .lightbox-close:hover {
        color: #fbbf24;
      }
      html {
        scroll-behavior: smooth;
      }
      body {
        font-family: "Cairo", "sans-serif";
      }

      .loading-skeleton {
        background: linear-gradient(
          90deg,
          #f0f0f0 25%,
          #e0e0e0 50%,
          #f0f0f0 75%
        );
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
      }

      @keyframes loading {
        0% {
          background-position: 200% 0;
        }
        100% {
          background-position: -200% 0;
        }
      }

      .image-container {
        position: relative;
        overflow: hidden;
        border-radius: 0.5rem;
        transition: transform 0.3s ease;
      }

      .image-container:hover {
        transform: scale(1.05);
      }

      .image-container img {
        width: 100%;
        height: 300px;
        object-fit: cover;
        transition: opacity 0.3s ease;
      }

      .image-overlay {
        position: absolute;
        inset: 0;
        background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
        opacity: 0;
        transition: opacity 0.3s ease;
        display: flex;
        align-items: end;
        padding: 1rem;
      }

      .image-container:hover .image-overlay {
        opacity: 1;
      }
    </style>
  </head>
  <body class="bg-brand-dark text-brand-light">
    <!-- شريط التنقل -->
    <nav
      class="fixed top-0 left-0 right-0 z-50 bg-brand-dark/90 backdrop-blur-md border-b border-brand-accent/20"
    >
      <div class="container mx-auto px-4 py-3">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4 space-x-reverse">
            <img
              src="assets/site/profile.jpg"
              alt="مؤسسة ركن النحت"
              class="w-10 h-10 rounded-full"
            />
            <h1
              class="text-xl font-bold text-brand-accent"
              data-translate="siteTitle"
            >
              مؤسسة ركن النحت
            </h1>
          </div>
          <div class="hidden md:flex items-center space-x-6 space-x-reverse">
            <a
              href="index.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              >الرئيسية</a
            >
            <a href="sculptures.html" class="text-brand-accent font-bold"
              >قسم النحت</a
            >
            <a
              href="decorations.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              >قسم الديكور</a
            >
            <a
              href="gallery.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              >معرض الأعمال</a
            >
            <a
              href="contact.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              >تواصل معنا</a
            >
          </div>
          <!-- زر تبديل اللغة -->
          <button
            id="lang-switch-btn"
            class="ml-4 px-4 py-2 rounded-lg bg-brand-accent text-brand-dark font-bold hover:bg-brand-accent/90 transition-all"
          >
            English
          </button>
          <button id="mobile-menu-btn" class="md:hidden text-brand-light">
            <svg
              class="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 6h16M4 12h16M4 18h16"
              ></path>
            </svg>
          </button>
        </div>
        <!-- القائمة المحمولة -->
        <div id="mobile-menu" class="hidden md:hidden mt-4 pb-4">
          <div class="flex flex-col space-y-3">
            <a
              href="index.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              >الرئيسية</a
            >
            <a href="sculptures.html" class="text-brand-accent font-bold"
              >قسم النحت</a
            >
            <a
              href="decorations.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              >قسم الديكور</a
            >
            <a
              href="gallery.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              >معرض الأعمال</a
            >
            <a
              href="contact.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              >تواصل معنا</a
            >
          </div>
        </div>
      </div>
    </nav>

    <!-- القسم الرئيسي -->
    <section class="pt-24 pb-12 bg-gradient-to-b from-brand-dark to-gray-900">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h1 class="text-5xl font-bold text-brand-accent mb-4">
            🎨 قسم النحت الحديث
          </h1>
          <p class="text-xl text-brand-light/80 max-w-3xl mx-auto">
            النحت على الخشب (بألواح BDF) والذي يتم تنفيذه بماكينة CNC هو فن بحد
            ذاته يجمع بين الرسم ومهارة النحت
          </p>
        </div>
      </div>
    </section>

    <!-- قسم مقدمة فن النحت -->
    <section class="py-16 bg-brand-dark">
      <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
          <div
            class="bg-gradient-to-r from-brand-accent/10 to-brand-accent/5 rounded-lg p-8 mb-12"
          >
            <h2 class="text-3xl font-bold text-brand-accent mb-6">
              مقدمة فن النحت
            </h2>
            <p class="text-lg text-brand-light/90 leading-relaxed mb-6">
              النحت على الخشب (بألواح BDF) والذي يتم تنفيذه بماكينة CNC هو فن
              بحد ذاته يجمع بين الرسم ومهارة النحت. لذلك، حاولت أن أخرجه من
              طريقه المعهود، كنحت الآيات القرآنية والنقشات، إلى الرسم التشكيلي،
              كنحت المناظر الطبيعية والحيوانية، ليصل إلى نحت الصور البشرية بدقة
              عالية، وذلك باستخدام المنشار اليدوي، دون الحاجة إلى الأدوات
              الكهربائية.
            </p>
            <div class="text-brand-accent font-semibold">
              📅 مارس 2024 - الآن
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- قسم أنواع النحت -->
    <section class="py-16 bg-gradient-to-b from-brand-dark to-gray-900">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-4xl font-bold text-brand-accent mb-4">أنواع النحت</h2>
          <p class="text-xl text-brand-light/80">
            تعرف على التقنيات والمواد المختلفة المستخدمة في أعمالنا
          </p>
        </div>

        <div class="overflow-x-auto">
          <table
            class="w-full bg-brand-dark border border-brand-accent/20 rounded-lg overflow-hidden"
          >
            <thead class="bg-brand-accent text-brand-dark">
              <tr>
                <th class="px-6 py-4 text-right font-bold">الرقم</th>
                <th class="px-6 py-4 text-right font-bold">
                  نوع فن النحت الحديث
                </th>
                <th class="px-6 py-4 text-right font-bold">المادة المستخدمة</th>
                <th class="px-6 py-4 text-right font-bold">الشكل النهائي</th>
              </tr>
            </thead>
            <tbody class="text-brand-light">
              <tr class="border-b border-brand-accent/10">
                <td class="px-6 py-4 font-bold text-brand-accent">1</td>
                <td class="px-6 py-4">نحت مجسمات جبسية</td>
                <td class="px-6 py-4">جبس+أسلاك+شاش</td>
                <td class="px-6 py-4">تماثيل ومجسمات واقعية</td>
              </tr>
              <tr class="border-b border-brand-accent/10">
                <td class="px-6 py-4 font-bold text-brand-accent">2</td>
                <td class="px-6 py-4">نحت مجسمات المكتبية</td>
                <td class="px-6 py-4">
                  منشار وغراء وصنفرة خشب أبو9 وعدة طبقات من أبو6
                </td>
                <td class="px-6 py-4">تماثيل ونحت تجريد معبر</td>
              </tr>
              <tr class="border-b border-brand-accent/10">
                <td class="px-6 py-4 font-bold text-brand-accent">3</td>
                <td class="px-6 py-4">نحت على الخشب بطريقة مشابهة للرسم</td>
                <td class="px-6 py-4">خشب أبو 3 وقماش قطيفة كخلفية</td>
                <td class="px-6 py-4">نحت تشكيلي معبر وصور لأشخاص</td>
              </tr>
              <tr>
                <td class="px-6 py-4 font-bold text-brand-accent">4</td>
                <td class="px-6 py-4">نحت مجسمات جدارية</td>
                <td class="px-6 py-4">جص وسكين خاص بالنحت</td>
                <td class="px-6 py-4">نحت مجسمات وتشكيلية على الجدران</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </section>

    <!-- قسم الفكرة الجديدة -->
    <section class="py-16 bg-brand-dark">
      <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
          <div
            class="bg-gradient-to-r from-brand-accent/10 to-brand-accent/5 rounded-lg p-8 mb-12"
          >
            <h2 class="text-3xl font-bold text-brand-accent mb-6">
              الفكرة الجديدة للنحت والمشاركات في المعارض
            </h2>
            <p class="text-lg text-brand-light/90 leading-relaxed mb-6">
              تتمحور الفكرة حول الخروج عن المألوف في فن النحت باستخدام ماكينة
              CNC، والانتقال من النقوش التقليدية إلى تجسيد فني إبداعي يشمل رسم
              المناظر الطبيعية، الحيوانات، وحتى الصور البشرية بدقة متناهية، مع
              لمسة يدوية فنية باستخدام المنشار اليدوي لإضفاء طابع فريد وشخصي على
              كل قطعة.
            </p>
            <div class="text-brand-accent font-semibold">
              📅 ديسمبر 2011 - مارس 2013
            </div>
          </div>

          <div
            class="bg-gradient-to-r from-red-500/10 to-red-500/5 rounded-lg p-8 border border-red-500/20"
          >
            <h3 class="text-2xl font-bold text-red-400 mb-4">
              🎬 مشاركة في معرض ظفار
            </h3>
            <p class="text-brand-light/80">
              فيديو يوضح مشاركة أعمال النحت في معرض ظفار بعمان
            </p>
            <div
              class="mt-4 p-4 bg-red-500/10 rounded border border-red-500/20"
            >
              <p class="text-red-300 text-sm">
                📹 الفيديو متاح للمشاهدة في المعرض الرئيسي
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- قسم معرض أعمال النحت -->
    <section class="py-16 bg-gradient-to-b from-brand-dark to-gray-900">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-4xl font-bold text-brand-accent mb-4">
            معرض أعمال النحت
          </h2>
          <p class="text-xl text-brand-light/80">
            مجموعة مختارة من أفضل أعمالنا الفنية
          </p>
        </div>

        <!-- شريط البحث -->
        <div class="max-w-md mx-auto mb-8">
          <input
            type="text"
            id="search-input"
            placeholder="البحث في الأعمال..."
            class="w-full px-4 py-3 bg-brand-dark border border-brand-accent/30 rounded-lg text-brand-light placeholder-brand-light/50 focus:border-brand-accent focus:outline-none"
          />
        </div>

        <!-- نافذة عرض الصورة (Lightbox) -->
        <div id="lightbox" class="lightbox-overlay" hidden>
          <span class="lightbox-close" id="lightbox-close">×</span>
          <img id="lightbox-img" class="lightbox-img" src="" alt="صورة مكبرة" />
        </div>
        <!-- شبكة الصور -->
        <div
          id="sculptures-grid"
          class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
        >
          <!-- سيتم تحميل الصور هنا بواسطة JavaScript -->
        </div>

        <!-- زر تحميل المزيد -->
        <div class="text-center mt-12">
          <button
            id="load-more"
            class="bg-brand-accent hover:bg-brand-accent/90 text-brand-dark px-8 py-3 rounded-lg font-bold transition-colors"
          >
            تحميل المزيد
          </button>
        </div>
      </div>
    </section>

    <!-- التذييل -->
    <footer class="bg-black text-brand-light py-12">
      <div class="container mx-auto px-4">
        <div class="grid md:grid-cols-3 gap-8">
          <div>
            <h3 class="text-xl font-bold text-brand-accent mb-4">
              مؤسسة ركن النحت
            </h3>
            <p class="text-brand-light/80">
              متخصصون في فن النحت الحديث والديكور الإبداعي، نقدم أعمال فنية
              متميزة تجمع بين الأصالة والابتكار.
            </p>
          </div>

          <div>
            <h3 class="text-xl font-bold text-brand-accent mb-4">
              روابط سريعة
            </h3>
            <ul class="space-y-2">
              <li>
                <a
                  href="index.html"
                  class="text-brand-light/80 hover:text-brand-accent transition-colors"
                  >الرئيسية</a
                >
              </li>
              <li>
                <a href="sculptures.html" class="text-brand-accent"
                  >قسم النحت</a
                >
              </li>
              <li>
                <a
                  href="decorations.html"
                  class="text-brand-light/80 hover:text-brand-accent transition-colors"
                  >قسم الديكور</a
                >
              </li>
              <li>
                <a
                  href="gallery.html"
                  class="text-brand-light/80 hover:text-brand-accent transition-colors"
                  >معرض الأعمال</a
                >
              </li>
              <li>
                <a
                  href="contact.html"
                  class="text-brand-light/80 hover:text-brand-accent transition-colors"
                  >تواصل معنا</a
                >
              </li>
            </ul>
          </div>

          <div>
            <h3 class="text-xl font-bold text-brand-accent mb-4">
              معلومات التواصل
            </h3>
            <div class="space-y-2 text-brand-light/80">
              <p>📧 <EMAIL></p>
              <p>📱 776245622</p>
              <p>📍 اليمن</p>
            </div>
          </div>
        </div>

        <div class="border-t border-brand-accent/20 mt-8 pt-8 text-center">
          <p class="text-brand-light/60">
            © 2024 مؤسسة ركن النحت - فهد منصور. جميع الحقوق محفوظة.
          </p>
        </div>
      </div>
    </footer>

    <script>
      // قائمة صور النحت
      const sculptureImages = [
        "aih.jpg",
        "aih2.jpg",
        "aih3.jpg",
        "alhmady.jpg",
        "atho.jpg",
        "azbiry.jpg",
        "bbb.jpg",
        "eelam.jpg",
        "fal.jpg",
        "hash.jpg",
        "hodyfa.jpg",
        "hza.jpg",
        "mabr.jpg",
        "mabr2.jpg",
        "mabr3.jpg",
        "mgasm.jpg",
        "mgsm.jpg",
        "n1.jpg",
        "n2.jpg",
        "n3.jpg",
        "n4.jpg",
        "n5.jpg",
        "n6.jpg",
        "n7.jpg",
        "n8.jpg",
        "n9.jpg",
        "n10.jpg",
        "n11.jpg",
        "n12.jpg",
        "n13.jpg",
        "n14.jpg",
        "n15.jpg",
        "n16.jpg",
        "n17.jpg",
        "n18.jpg",
        "n19.jpg",
        "n20.jpg",
        "n21.jpg",
        "n22.jpg",
        "n23.jpg",
        "n24.jpg",
        "n25.jpg",
        "n26.jpg",
        "n27.jpg",
        "n28.jpg",
        "n29.jpg",
        "rr (1).png",
        "rr (10).jpg",
        "rr (11).jpg",
        "rr (12).jpg",
        "rr (13).jpg",
        "rr (14).jpg",
        "rr (16).jpg",
        "rr (17).jpg",
        "rr (18).jpg",
        "rr (19).jpg",
        "rr (2).jpg",
        "rr (20).jpg",
        "rr (3).jpg",
        "rr (4).jpg",
        "rr (51).png",
        "rr (52).png",
        "rr (53).jpg",
        "rr (6).jpg",
        "rr (7).jpg",
        "rr (8).jpg",
        "rr (9).jpg",
        "rr(21).png",
        "rr(22).jpg",
        "rr(5).jpg",
        "rr(50).jpg",
        "sabr.jpg",
        "twakl.jpg",
      ];

      let currentPage = 0;
      const imagesPerPage = 12;
      let filteredImages = [...sculptureImages];

      // تفعيل القائمة المحمولة
      document
        .getElementById("mobile-menu-btn")
        .addEventListener("click", function () {
          const mobileMenu = document.getElementById("mobile-menu");
          mobileMenu.classList.toggle("hidden");
        });

      // وظيفة تحميل الصور
      function loadImages() {
        const grid = document.getElementById("sculptures-grid");
        const startIndex = currentPage * imagesPerPage;
        const endIndex = Math.min(
          startIndex + imagesPerPage,
          filteredImages.length
        );

        for (let i = startIndex; i < endIndex; i++) {
          const imageName = filteredImages[i];
          const imageDiv = document.createElement("div");
          imageDiv.className = "image-container";
          imageDiv.innerHTML = `
            <img src="assets/sculptures/${imageName}" alt="عمل نحت ${i + 1}"
                 class="loading-skeleton" loading="lazy"
                 onerror="this.style.display='none'">
            <div class="image-overlay">
              <div class="text-white">
                <h3 class="font-bold">عمل نحت ${i + 1}</h3>
                <p class="text-sm opacity-80">عمل فني متميز</p>
              </div>
            </div>
          `;
          // إضافة حدث الضغط لفتح الصورة في نافذة منبثقة
          imageDiv.querySelector("img").addEventListener("click", function () {
            openLightbox(`assets/sculptures/${imageName}`);
          });
          grid.appendChild(imageDiv);
        }

        currentPage++;

        // إخفاء زر "تحميل المزيد" إذا تم تحميل جميع الصور
        if (endIndex >= filteredImages.length) {
          document.getElementById("load-more").style.display = "none";
        }
      }
      // نافذة عرض الصورة (Lightbox)
      function openLightbox(src) {
        const lightbox = document.getElementById("lightbox");
        const lightboxImg = document.getElementById("lightbox-img");
        lightboxImg.src = src;
        lightbox.removeAttribute("hidden");
        document.body.style.overflow = "hidden";
      }
      document.getElementById("lightbox-close").onclick = function () {
        document.getElementById("lightbox").setAttribute("hidden", "");
        document.getElementById("lightbox-img").src = "";
        document.body.style.overflow = "";
      };
      // إغلاق النافذة عند الضغط على الخلفية
      document.getElementById("lightbox").onclick = function (e) {
        if (e.target === this) {
          this.setAttribute("hidden", "");
          document.getElementById("lightbox-img").src = "";
          document.body.style.overflow = "";
        }
      };

      // وظيفة البحث
      function filterImages(searchTerm) {
        filteredImages = sculptureImages.filter((image) =>
          image.toLowerCase().includes(searchTerm.toLowerCase())
        );

        // إعادة تعيين الشبكة
        document.getElementById("sculptures-grid").innerHTML = "";
        currentPage = 0;
        document.getElementById("load-more").style.display = "block";

        loadImages();
      }

      // تفعيل البحث
      document
        .getElementById("search-input")
        .addEventListener("input", function (e) {
          filterImages(e.target.value);
        });

      // تفعيل زر "تحميل المزيد"
      document
        .getElementById("load-more")
        .addEventListener("click", loadImages);

      // تحميل الصور الأولى عند تحميل الصفحة
      document.addEventListener("DOMContentLoaded", function () {
        loadImages();
      });

      // تسجيل Service Worker
      if ("serviceWorker" in navigator) {
        window.addEventListener("load", function () {
          navigator.serviceWorker
            .register("sw.js")
            .then(function (registration) {
              // Service Worker registered successfully
            });
        });
      }
    </script>
    <script>
      // زر تبديل اللغة
      document.addEventListener("DOMContentLoaded", function () {
        var langBtn = document.getElementById("lang-switch-btn");
        if (langBtn) {
          langBtn.addEventListener("click", function () {
            var currentLang =
              localStorage.getItem("siteLanguage") ||
              document.documentElement.lang ||
              "ar";
            var newLang = currentLang === "ar" ? "en" : "ar";
            localStorage.setItem("siteLanguage", newLang);
            document.documentElement.lang = newLang;
            if (
              window.languageManager &&
              typeof window.languageManager.applyLanguage === "function"
            ) {
              window.languageManager.applyLanguage(newLang);
            }
            // لا حاجة لإعادة تحميل الصفحة
          });
          var currentLang =
            localStorage.getItem("siteLanguage") ||
            document.documentElement.lang ||
            "ar";
          langBtn.textContent = currentLang === "ar" ? "English" : "العربية";
        }
      });
    </script>

    <!-- تطبيق نظام الترجمة -->
    <script src="assets/js/apply-translations.js"></script>
  </body>
</html>
