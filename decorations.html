<script>
  // تفعيل الإشعارات تلقائياً إذا كان المستخدم مشتركاً من الصفحة الرئيسية
  document.addEventListener("DOMContentLoaded", function () {
    if (localStorage.getItem("notificationsSubscribed") === "true") {
      if ("Notification" in window && Notification.permission === "granted") {
        // إشعار ترحيبي أو أي إجراء آخر
        // new Notification("مرحباً بك مجدداً! ستصلك إشعارات بكل جديد.");
      } else if (
        "Notification" in window &&
        Notification.permission !== "denied"
      ) {
        Notification.requestPermission();
      }
    }
  });
</script>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <style>
      .responsive-img {
        max-width: 100%;
        height: auto;
        display: block;
        margin-left: auto;
        margin-right: auto;
        border-radius: 14px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.13);
        transition: transform 0.3s, box-shadow 0.3s;
        cursor: pointer;
      }
      .responsive-img:hover {
        transform: scale(1.04);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.18);
      }
    </style>
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        document
          .querySelectorAll(
            'img:not(.responsive-img):not([id^="modal"]):not([id^="lightbox"])'
          )
          .forEach(function (img) {
            img.classList.add("responsive-img");
            img.setAttribute("loading", "lazy");
          });
      });
    </script>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>قسم الديكور - مؤسسة ركن النحت</title>
    <meta
      name="description"
      content="اكتشف تصاميم الديكور الحديثة والإبداعية في مؤسسة ركن النحت"
    />
    <meta
      name="keywords"
      content="ديكور, تصميم داخلي, أعمال جبس, خشب, تصاميم حديثة, فهد منصور, ركن النحت"
    />
    <meta name="author" content="فهد منصور" />

    <!-- Canonical Link -->
    <link rel="canonical" href="https://fahdmansor.surge.sh/decorations.html" />

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="قسم الديكور - مؤسسة ركن النحت" />
    <meta
      property="og:description"
      content="اكتشف تصاميم الديكور الحديثة والإبداعية في مؤسسة ركن النحت"
    />
    <meta property="og:image" content="assets/site/profile2.png" />
    <meta property="og:type" content="website" />
    <meta
      property="og:url"
      content="https://fahdmansor.surge.sh/decorations.html"
    />

    <!-- SEO Meta Tags -->
    <meta name="robots" content="index, follow" />

    <!-- أيقونات الموقع -->
    <link rel="icon" type="image/jpeg" href="assets/site/profile.jpg" />
    <link rel="manifest" href="manifest.json" />
    <meta name="theme-color" content="#FBBF24" />

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- نظام إدارة اللغات -->
    <script src="assets/js/translations.js"></script>
    <script src="assets/js/language-manager.js"></script>
    <link rel="stylesheet" href="assets/css/language-styles.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700;900&display=swap"
      rel="stylesheet"
    />

    <script>
      tailwind.config = {
        theme: {
          extend: {
            fontFamily: {
              sans: ["Cairo", "sans-serif"],
            },
            colors: {
              "brand-dark": "#111827",
              "brand-light": "#ffffff",
              "brand-accent": {
                DEFAULT: "#F59E0B",
                start: "#FBBF24",
                end: "#D97706",
              },
            },
          },
        },
      };
    </script>

    <style>
      html {
        scroll-behavior: smooth;
      }
      body {
        font-family: "Cairo", "sans-serif";
      }

      .loading-skeleton {
        background: linear-gradient(
          90deg,
          #f0f0f0 25%,
          #e0e0e0 50%,
          #f0f0f0 75%
        );
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
      }

      @keyframes loading {
        0% {
          background-position: 200% 0;
        }
        100% {
          background-position: -200% 0;
        }
      }

      .image-container {
        position: relative;
        overflow: hidden;
        border-radius: 0.5rem;
        transition: transform 0.3s ease;
      }

      .image-container:hover {
        transform: scale(1.05);
      }

      .image-container img {
        width: 100%;
        height: 300px;
        object-fit: cover;
        transition: opacity 0.3s ease;
      }

      .image-overlay {
        position: absolute;
        inset: 0;
        background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
        opacity: 0;
        transition: opacity 0.3s ease;
        display: flex;
        align-items: end;
        padding: 1rem;
      }

      .image-container:hover .image-overlay {
        opacity: 1;
      }
    </style>
  </head>
  <body class="bg-brand-dark text-brand-light">
    <!-- شريط التنقل -->
    <nav
      class="fixed top-0 left-0 right-0 z-50 bg-brand-dark/90 backdrop-blur-md border-b border-brand-accent/20"
    >
      <div class="container mx-auto px-4 py-3">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4 space-x-reverse">
            <img
              src="assets/site/profile.jpg"
              alt="مؤسسة ركن النحت"
              class="w-10 h-10 rounded-full"
            />
            <h1 class="text-xl font-bold text-brand-accent">مؤسسة ركن النحت</h1>
          </div>
          <div class="hidden md:flex items-center space-x-6 space-x-reverse">
            <a
              href="index.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              >الرئيسية</a
            >
            <a
              href="sculptures.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              >قسم النحت</a
            >
            <a href="decorations.html" class="text-brand-accent font-bold"
              >قسم الديكور</a
            >
            <a
              href="gallery.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              >معرض الأعمال</a
            >
            <a
              href="contact.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              >تواصل معنا</a
            >
          </div>
          <!-- زر تبديل اللغة -->
          <button
            id="lang-switch-btn"
            class="ml-4 px-4 py-2 rounded-lg bg-brand-accent text-brand-dark font-bold hover:bg-brand-accent/90 transition-all"
          >
            English
          </button>
          <button id="mobile-menu-btn" class="md:hidden text-brand-light">
            <svg
              class="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 6h16M4 12h16M4 18h16"
              ></path>
            </svg>
          </button>
        </div>
        <!-- القائمة المحمولة -->
        <div id="mobile-menu" class="hidden md:hidden mt-4 pb-4">
          <div class="flex flex-col space-y-3">
            <a
              href="index.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              >الرئيسية</a
            >
            <a
              href="sculptures.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              >قسم النحت</a
            >
            <a href="decorations.html" class="text-brand-accent font-bold"
              >قسم الديكور</a
            >
            <a
              href="gallery.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              >معرض الأعمال</a
            >
            <a
              href="contact.html"
              class="text-brand-light hover:text-brand-accent transition-colors"
              >تواصل معنا</a
            >
          </div>
        </div>
      </div>
    </nav>

    <!-- القسم الرئيسي -->
    <section class="pt-24 pb-12 bg-gradient-to-b from-brand-dark to-gray-900">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h1 class="text-5xl font-bold text-brand-accent mb-4">
            🏠 قسم الديكور
          </h1>
          <p class="text-xl text-brand-light/80 max-w-3xl mx-auto">
            فن الديكور هو أحد الفنون الإبداعية التي تجمع بين الجمال والوظيفة
            لتشكيل بيئات مريحة وجذابة
          </p>
        </div>
      </div>
    </section>

    <!-- قسم مقدمة فن الديكور -->
    <section class="py-16 bg-brand-dark">
      <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
          <div
            class="bg-gradient-to-r from-brand-accent/10 to-brand-accent/5 rounded-lg p-8 mb-12"
          >
            <h2 class="text-3xl font-bold text-brand-accent mb-6">
              مقدمة لفن الديكور والتصاميم
            </h2>
            <p class="text-lg text-brand-light/90 leading-relaxed mb-6">
              فن الديكور هو أحد الفنون الإبداعية التي تجمع بين الجمال والوظيفة
              لتشكيل بيئات مريحة وجذابة تلبي احتياجات الإنسان العصرية. في عصرنا
              الحديث، أصبحت الديكورات أكثر من مجرد تزيين للمساحات، بل هي انعكاس
              لشخصيات الأفراد وأسلوب حياتهم. الديكورات الحديثة تميزت باستخدام
              الألوان الهادئة والتفاصيل البسيطة التي تمنح الشعور بالراحة، مع
              الاعتماد على الإضاءة المدروسة لتعزيز جمالية المكان.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- قسم المتطلبات الأساسية -->
    <section class="py-16 bg-gradient-to-b from-brand-dark to-gray-900">
      <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
          <div
            class="bg-gradient-to-r from-blue-500/10 to-blue-500/5 rounded-lg p-8 mb-12 border border-blue-500/20"
          >
            <h2 class="text-3xl font-bold text-blue-400 mb-6">
              متطلبات أساسية
            </h2>
            <h3 class="text-xl font-semibold text-brand-accent mb-4">
              قبل بداية التنفيذ أو التطبيق العملي للديكور لابد من:
            </h3>
            <ul class="space-y-4 text-lg text-brand-light/90">
              <li class="flex items-start">
                <span class="text-blue-400 ml-3">•</span>
                التعرف على المواد المستخدمة تنفيذ السقوف الصناعية، وكذلك النحت
                بالجبس، أو الجص، أو الخشب.
              </li>
              <li class="flex items-start">
                <span class="text-blue-400 ml-3">•</span>
                دراسة بل وتطبيق الجانب النظري، المتمثل في، الرسم الهندسي.
              </li>
              <li class="flex items-start">
                <span class="text-blue-400 ml-3">•</span>
                عمل زيارات ميدانية جماعية، لمحلات بيع مواد الديكور، وكذلك لمواقع
                نُفذت مسبقاً.
              </li>
              <li class="flex items-start">
                <span class="text-blue-400 ml-3">•</span>
                التعرف على أسعار مواد الديكور وكيفية التعامل معها.
              </li>
            </ul>
            <div class="text-blue-400 font-semibold mt-6">
              📅 أغسطس 2006 - مايو 2010
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- قسم أنواع الديكورات -->
    <section class="py-16 bg-brand-dark">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-4xl font-bold text-brand-accent mb-4">
            أنواع الديكورات
          </h2>
          <p class="text-xl text-brand-light/80">
            تعرف على التقنيات والمواد المختلفة المستخدمة في تصاميمنا
          </p>
        </div>

        <div class="overflow-x-auto">
          <table
            class="w-full bg-brand-dark border border-brand-accent/20 rounded-lg overflow-hidden"
          >
            <thead class="bg-brand-accent text-brand-dark">
              <tr>
                <th class="px-6 py-4 text-right font-bold">الرقم</th>
                <th class="px-6 py-4 text-right font-bold">نوع الفن</th>
                <th class="px-6 py-4 text-right font-bold">المادة المستخدمة</th>
                <th class="px-6 py-4 text-right font-bold">الشكل النهائي</th>
              </tr>
            </thead>
            <tbody class="text-brand-light">
              <tr class="border-b border-brand-accent/10">
                <td class="px-6 py-4 font-bold text-brand-accent">1</td>
                <td class="px-6 py-4">الديكور الحديث</td>
                <td class="px-6 py-4">مواد مختلفة</td>
                <td class="px-6 py-4">تصاميم معاصرة</td>
              </tr>
              <tr>
                <td class="px-6 py-4 font-bold text-brand-accent">2</td>
                <td class="px-6 py-4">الديكور التقليدي</td>
                <td class="px-6 py-4">خشب وحجر</td>
                <td class="px-6 py-4">تصاميم كلاسيكية</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </section>

    <!-- قسم التطبيقات العملية -->
    <section class="py-16 bg-gradient-to-b from-brand-dark to-gray-900">
      <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
          <div
            class="bg-gradient-to-r from-green-500/10 to-green-500/5 rounded-lg p-8 mb-12 border border-green-500/20"
          >
            <h2 class="text-3xl font-bold text-green-400 mb-6">
              تطبيقات عملية
            </h2>
            <h3 class="text-xl font-semibold text-brand-accent mb-4">
              التطبيقات العملية للديكور في المؤسسات الحكومية
            </h3>
            <p class="text-lg text-brand-light/90 leading-relaxed mb-6">
              تم النزول الميداني إلى مدرسة الكويت بمديرية صالة، وتحديداً إلى
              (صالة الاجتماعات) بتاريخ 25/10/2023، حيث تم تطبيق مشروع (ديكور
              عملي) للدفعة السابعة التابعة لمؤسسة "معكم"، والذي تم الانتهاء منه
              تقريباً في 25/8/2023.
            </p>
            <p class="text-lg text-brand-light/90 leading-relaxed mb-6">
              كما تم النزول الميداني إلى معهد "احتراف" الذي يقوم بتدريب الدفعة
              الثانية من طلاب الديكور العملي في (صالة الاجتماعات) بمدرسة ناصر
              بمديرية القاهرة، والذي بدأ بتاريخ 28/8/2023 وما زال مستمراً.
            </p>
            <div
              class="bg-green-500/10 rounded-lg p-4 border border-green-500/20"
            >
              <p class="text-green-300 font-semibold">
                📚 انقر هنا لفتح البحث النظري والعملي
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- قسم معرض أعمال الديكور -->
    <section class="py-16 bg-brand-dark">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-4xl font-bold text-brand-accent mb-4">
            معرض أعمال الديكور
          </h2>
          <p class="text-xl text-brand-light/80">
            مجموعة مختارة من أفضل تصاميمنا
          </p>
        </div>

        <!-- شريط البحث -->
        <div class="max-w-md mx-auto mb-8">
          <input
            type="text"
            id="search-input"
            placeholder="البحث في التصاميم..."
            class="w-full px-4 py-3 bg-brand-dark border border-brand-accent/30 rounded-lg text-brand-light placeholder-brand-light/50 focus:border-brand-accent focus:outline-none"
          />
        </div>

        <!-- شبكة الصور -->
        <div
          id="decorations-grid"
          class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
        >
          <!-- سيتم تحميل الصور هنا بواسطة JavaScript -->
        </div>

        <!-- زر تحميل المزيد -->
        <div class="text-center mt-12">
          <button
            id="load-more"
            class="bg-brand-accent hover:bg-brand-accent/90 text-brand-dark px-8 py-3 rounded-lg font-bold transition-colors"
          >
            تحميل المزيد
          </button>
        </div>
      </div>
    </section>

    <!-- التذييل -->
    <footer class="bg-black text-brand-light py-12">
      <div class="container mx-auto px-4">
        <div class="grid md:grid-cols-3 gap-8">
          <div>
            <h3 class="text-xl font-bold text-brand-accent mb-4">
              مؤسسة ركن النحت
            </h3>
            <p class="text-brand-light/80">
              متخصصون في فن النحت الحديث والديكور الإبداعي، نقدم أعمال فنية
              متميزة تجمع بين الأصالة والابتكار.
            </p>
          </div>

          <div>
            <h3 class="text-xl font-bold text-brand-accent mb-4">
              روابط سريعة
            </h3>
            <ul class="space-y-2">
              <li>
                <a
                  href="index.html"
                  class="text-brand-light/80 hover:text-brand-accent transition-colors"
                  >الرئيسية</a
                >
              </li>
              <li>
                <a
                  href="sculptures.html"
                  class="text-brand-light/80 hover:text-brand-accent transition-colors"
                  >قسم النحت</a
                >
              </li>
              <li>
                <a href="decorations.html" class="text-brand-accent"
                  >قسم الديكور</a
                >
              </li>
              <li>
                <a
                  href="gallery.html"
                  class="text-brand-light/80 hover:text-brand-accent transition-colors"
                  >معرض الأعمال</a
                >
              </li>
              <li>
                <a
                  href="contact.html"
                  class="text-brand-light/80 hover:text-brand-accent transition-colors"
                  >تواصل معنا</a
                >
              </li>
            </ul>
          </div>

          <div>
            <h3 class="text-xl font-bold text-brand-accent mb-4">
              معلومات التواصل
            </h3>
            <div class="space-y-2 text-brand-light/80">
              <p>📧 <EMAIL></p>
              <p>📱 776245622</p>
              <p>📍 اليمن</p>
            </div>
          </div>
        </div>

        <div class="border-t border-brand-accent/20 mt-8 pt-8 text-center">
          <p class="text-brand-light/60">
            © 2024 مؤسسة ركن النحت - فهد منصور. جميع الحقوق محفوظة.
          </p>
        </div>
      </div>
    </footer>

    <script>
      // قائمة صور الديكور
      const decorationImages = [
        "d1.jpg",
        "d2.jpg",
        "d3.jpg",
        "d4.jpg",
        "d5.jpg",
        "d6.jpg",
        "d7.jpg",
        "d8.jpg",
        "d9.jpg",
        "d10.jpg",
        "d11.jpg",
        "d12.jpg",
        "d13.jpg",
        "d14.jpg",
        "d15.jpg",
        "d16.jpg",
        "d17.jpg",
        "d18.jpg",
        "d19.jpg",
        "d20.jpg",
        "d21.jpg",
        "d22.jpg",
        "d23.jpg",
        "d24.jpg",
        "d25.jpg",
        "d26.jpg",
        "d27.jpg",
        "d28.jpg",
        "d29.jpg",
        "d30.jpg",
        "d31.jpg",
        "d32.jpg",
        "d33.jpg",
        "d34.jpg",
        "d35.jpg",
        "d36.jpg",
        "asa.jpg",
        "back.jpg",
        "cust.jpg",
        "deecor.jpg",
        "dek.jpg",
        "dicor.jpg",
        "gbs.jpg",
        "qqw.jpg",
        "school.jpg",
        "school2.jpg",
        "shop1.jpg",
        "shop2.jpg",
        "shop3.jpg",
        "shop4.jpg",
        "shop5.jpg",
        "shop6.jpg",
        "wall.jpg",
        "wall2.jpg",
        "wall5.jpg",
        "wall6.jpg",
        "wall7.jpg",
        "wws.jpg",
      ];

      let currentPage = 0;
      const imagesPerPage = 8;
      let filteredImages = [...decorationImages];

      // تفعيل القائمة المحمولة
      document
        .getElementById("mobile-menu-btn")
        .addEventListener("click", function () {
          const mobileMenu = document.getElementById("mobile-menu");
          mobileMenu.classList.toggle("hidden");
        });

      // وظيفة تحميل الصور
      function loadImages() {
        const grid = document.getElementById("decorations-grid");
        const startIndex = currentPage * imagesPerPage;
        const endIndex = Math.min(
          startIndex + imagesPerPage,
          filteredImages.length
        );

        for (let i = startIndex; i < endIndex; i++) {
          const imageName = filteredImages[i];
          const imageDiv = document.createElement("div");
          imageDiv.className = "image-container";
          imageDiv.innerHTML = `
                    <img src="assets/decorations/${imageName}" alt="تصميم ديكور ${
            i + 1
          }" 
                         class="loading-skeleton" loading="lazy"
                         onerror="this.style.display='none'">
                    <div class="image-overlay">
                        <div class="text-white">
                            <h3 class="font-bold">تصميم ديكور ${i + 1}</h3>
                            <p class="text-sm opacity-80">تصميم عصري ومبتكر</p>
                        </div>
                    </div>
                `;
          grid.appendChild(imageDiv);
        }

        currentPage++;

        // إخفاء زر "تحميل المزيد" إذا تم تحميل جميع الصور
        if (endIndex >= filteredImages.length) {
          document.getElementById("load-more").style.display = "none";
        }
      }

      // وظيفة البحث
      function filterImages(searchTerm) {
        filteredImages = decorationImages.filter((image) =>
          image.toLowerCase().includes(searchTerm.toLowerCase())
        );

        // إعادة تعيين الشبكة
        document.getElementById("decorations-grid").innerHTML = "";
        currentPage = 0;
        document.getElementById("load-more").style.display = "block";

        loadImages();
      }

      // تفعيل البحث
      document
        .getElementById("search-input")
        .addEventListener("input", function (e) {
          filterImages(e.target.value);
        });

      // تفعيل زر "تحميل المزيد"
      document
        .getElementById("load-more")
        .addEventListener("click", loadImages);

      // تحميل الصور الأولى عند تحميل الصفحة
      document.addEventListener("DOMContentLoaded", function () {
        loadImages();
      });

      // تسجيل Service Worker
      if ("serviceWorker" in navigator) {
        window.addEventListener("load", function () {
          navigator.serviceWorker
            .register("sw.js")
            .then(function (registration) {
              // Service Worker registered successfully
            });
        });
      }
    </script>
    <script>
      // زر تبديل اللغة
      document.addEventListener("DOMContentLoaded", function () {
        var langBtn = document.getElementById("lang-switch-btn");
        if (langBtn) {
          langBtn.addEventListener("click", function () {
            var currentLang = window.languageManager
              ? window.languageManager.getCurrentLanguage()
              : localStorage.getItem("preferred-language") ||
                document.documentElement.lang ||
                "ar";
            var newLang = currentLang === "ar" ? "en" : "ar";
            if (window.languageManager) {
              window.languageManager.applyLanguage(newLang);
              langBtn.textContent = newLang === "ar" ? "English" : "العربية";
            }
          });
          var currentLang = window.languageManager
            ? window.languageManager.getCurrentLanguage()
            : localStorage.getItem("preferred-language") ||
              document.documentElement.lang ||
              "ar";
          langBtn.textContent = currentLang === "ar" ? "English" : "العربية";
        }
      });
    </script>

    <!-- تطبيق نظام الترجمة -->
    <script src="assets/js/apply-translations.js"></script>
  </body>
</html>
